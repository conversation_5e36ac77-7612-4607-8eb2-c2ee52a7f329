﻿using GamesEngine;
using GamesEngine.Business.Liquidity;
using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.ExternalServices;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Custodian;
using GamesEngine.Domains;
using GamesEngine.Settings;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineTests.Unit_Tests.Business
{
    [TestClass]
    public class TankerTest
    {
        DateTime defaultStartDate = new DateTime(2000, 1, 1);
        DateTime defaultEndDate = new DateTime(2025, 12, 28);

        [TestInitialize]
        public void TestInitialize()
        {
            LiquidFlow.ClearInstance();
            CurrenciesTest.AddCurrencies();
            ExecutionContext.Current.SetContext(DateTime.Now, false, new TestActor());
        }

        #region Helper Methods

        /// <summary>
        /// Sets up a standard test environment with a Liquid instance, domain, and other necessary components.
        /// </summary>
        private (Liquid liquid, Domain domain, bool itIsThePresent) SetupTestEnvironment()
        {
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            var allDomains = new List<Domain> { domain };
            Liquid liquid = LiquidFlow.Instance(allDomains).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquid.Source.AddXpub(new Xpub("xpub_for_testing"));
            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");
            return (liquid, domain, itIsThePresent);
        }

        /// <summary>
        /// Creates a draft deposit and immediately confirms it.
        /// </summary>
        private GamesEngine.Business.Liquidity.Transactions.Deposit CreateAndConfirmDeposit(Source source, Domain domain, DateTime createdAt, decimal amount = 1m)
        {
            bool itIsThePresent = false;
            int nextId = source.NextDepositId();
            var deposit = source.CreateDraftDeposit(itIsThePresent, createdAt, nextId, $"invoice{Guid.NewGuid()}", 1, nextId, "some_address", amount, 50000m, "USD", 100, 1, 1, domain);
            source.ConfirmDeposit(itIsThePresent, createdAt, deposit);
            return deposit;
        }

        #endregion

        #region Tests for AddDepositsByIds

        [TestMethod]
        public void AddDepositsByIds_WithValidIds_AddsDepositsToRootTank()
        {
            // Arrange
            var (liquid, domain, itIsThePresent) = SetupTestEnvironment();
            var source = liquid.Source;
            var now = DateTime.Now;

            var dateFeb = new DateTime(2023, 2, 10);
            var depositFeb2 = CreateAndConfirmDeposit(source, domain, dateFeb, 5m);

            int jarVersion = source.NextJarVersion();
            var tankFeb = source.Jar.CreateTank(itIsThePresent, dateFeb, source.NextTankId(), "TankFeb", "Desc", jarVersion, new List<int> { depositFeb2.Id });

            // Create some deposits in the Jar
            var deposit1 = CreateAndConfirmDeposit(source, domain, now, 1.5m);
            var deposit2 = CreateAndConfirmDeposit(source, domain, now, 2.5m);

            // Create a tanker (it will have an empty root tank initially)
            TankerPending tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "TestTanker", "Desc", new List<int>() { tankFeb.Id });
            Assert.AreEqual(5m, tanker.Amount, "Initial tanker amount should be 5.");
            var rootTank = tanker.Tanks.OfType<Tank.TankRoot>().Single();
            Assert.AreEqual(0, rootTank.ExplandedDeposits.Count(), "Root tank should be empty initially.");

            // Act
            var depositIdsToAdd = new List<int> { deposit1.Id, deposit2.Id };
            tanker.AddDepositsByIds(depositIdsToAdd);

            tanker = source.FindTanker(tanker.Id) as TankerPending;

            // Assert
            rootTank = tanker.Tanks.OfType<Tank.TankRoot>().Single();
            Assert.AreEqual(9.0m, tanker.Amount, "Tanker amount should be updated with the sum of deposit amounts.");
            Assert.AreEqual(2, rootTank.ExplandedDeposits.Count(), "Root tank should now contain the two added deposits.");
            Assert.IsTrue(rootTank.ExplandedDeposits.Any(d => d.Id == deposit1.Id));
            Assert.IsTrue(rootTank.ExplandedDeposits.Any(d => d.Id == deposit2.Id));
        }

        [TestMethod]
        public void AddDepositsByIds_WithNonExistentId_ThrowsGameEngineException()
        {
            // Arrange
            var (liquid, domain, itIsThePresent) = SetupTestEnvironment();
            var source = liquid.Source;
            var now = DateTime.Now;

            var dateFeb = new DateTime(2023, 2, 10);
            var depositFeb2 = CreateAndConfirmDeposit(source, domain, dateFeb, 5m);

            int jarVersion = source.NextJarVersion();
            var tankFeb = source.Jar.CreateTank(itIsThePresent, dateFeb, source.NextTankId(), "TankFeb", "Desc", jarVersion, new List<int> { depositFeb2.Id });

            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "TestTanker", "Desc", new List<int>() { tankFeb.Id });
            var nonExistentId = 99999;
            var depositIdsToAdd = new List<int> { nonExistentId };

            // Act & Assert
            var ex = Assert.ThrowsException<KeyNotFoundException>(() => tanker.AddDepositsByIds(depositIdsToAdd));
            Assert.AreEqual($"The given key '{nonExistentId}' was not present in the dictionary.", ex.Message);
        }

        [TestMethod]
        public void AddDepositsByIds_WithNullOrEmptyList_DoesNothing()
        {
            // Arrange
            var (liquid, domain, itIsThePresent) = SetupTestEnvironment();
            var source = liquid.Source;
            var now = DateTime.Now;

            var dateFeb = new DateTime(2023, 2, 10);
            var depositFeb2 = CreateAndConfirmDeposit(source, domain, dateFeb, 5m);

            int jarVersion = source.NextJarVersion();
            var tankFeb = source.Jar.CreateTank(itIsThePresent, dateFeb, source.NextTankId(), "TankFeb", "Desc", jarVersion, new List<int> { depositFeb2.Id });

            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "TestTanker", "Desc", new List<int>() { tankFeb.Id });
            var initialAmount = tanker.Amount;
            var rootTank = tanker.Tanks.OfType<Tank.TankRoot>().Single();
            var initialDepositCount = rootTank.ExplandedDeposits.Count();

            // Act (with null list)
            tanker.AddDepositsByIds(null);

            // Assert
            Assert.AreEqual(initialAmount, tanker.Amount, "Amount should not change for null list.");
            Assert.AreEqual(initialDepositCount, rootTank.ExplandedDeposits.Count(), "Deposit count should not change for null list.");

            // Act (with empty list)
            tanker.AddDepositsByIds(new List<int>());

            // Assert
            Assert.AreEqual(initialAmount, tanker.Amount, "Amount should not change for empty list.");
            Assert.AreEqual(initialDepositCount, rootTank.ExplandedDeposits.Count(), "Deposit count should not change for empty list.");
        }

        #endregion

        #region Tests for BuildMonthlySummary

        /// <summary>
        /// Sets up a consistent Tanker with tanks and root deposits across multiple months for summary testing.
        /// </summary>
        private Tanker.TankerPending SetupForSummaryTests()
        {
            var (liquid, domain, itIsThePresent) = SetupTestEnvironment();
            var source = liquid.Source;
            var now = DateTime.Now;

            // --- Create Deposits and Tanks for different months ---
            var dateJan = new DateTime(2024, 1, 10);
            var dateFeb = new DateTime(2024, 2, 15);
            var dateMar = new DateTime(2024, 3, 20);

            // January Tank
            var depJan = CreateAndConfirmDeposit(source, domain, dateJan, 100m);
            var tankJan = source.Jar.CreateTank(itIsThePresent, dateJan, source.NextTankId(), "Alpha Jan Tank", "Desc", source.NextJarVersion(), new List<int> { depJan.Id });
            tankJan.ChangeColor("#FFFF00"); // Yellow

            // February Tanks
            var depFeb1 = CreateAndConfirmDeposit(source, domain, dateFeb, 200m);
            var tankFeb1 = source.Jar.CreateTank(itIsThePresent, dateFeb, source.NextTankId(), "Beta Feb Tank", "Desc", source.NextJarVersion(), new List<int> { depFeb1.Id });
            tankFeb1.ChangeColor("#0000FF"); // Blue

            var depFeb2 = CreateAndConfirmDeposit(source, domain, dateFeb, 50m);
            var tankFeb2 = source.Jar.CreateTank(itIsThePresent, dateFeb, source.NextTankId(), "Gamma Feb Tank", "Desc", source.NextJarVersion(), new List<int> { depFeb2.Id });
            tankFeb2.ChangeColor("#FFFF00"); // Yellow

            // March Tank
            var depMar = CreateAndConfirmDeposit(source, domain, dateMar, 300m);
            var tankMar = source.Jar.CreateTank(itIsThePresent, dateMar, source.NextTankId(), "Alpha Mar Tank", "Desc", source.NextJarVersion(), new List<int> { depMar.Id });

            // --- Create Root Deposits for different months ---
            var rootDepositJan = CreateAndConfirmDeposit(source, domain, dateJan, 10m);
            var rootDepositFeb = CreateAndConfirmDeposit(source, domain, dateFeb, 20m);
            var rootDepositMar = CreateAndConfirmDeposit(source, domain, dateMar, 30m);

            // --- Create the Tanker ---
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "Summary Test Tanker", "Desc", new List<int> { tankJan.Id, tankFeb1.Id, tankFeb2.Id, tankMar.Id });

            // Add root deposits
            tanker.AddDepositsByIds(new List<int> { rootDepositJan.Id, rootDepositFeb.Id, rootDepositMar.Id });
            
            tanker = source.FindTanker(tanker.Id) as TankerPending;

            // Total Amount = (100) + (200 + 50) + (300) + (10 + 20 + 30) = 710
            Assert.AreEqual(710m, tanker.Amount, "Initial amount in setup is incorrect.");

            return tanker;
        }

        [TestMethod]
        public void BuildMonthlySummary_NoFilters_ReturnsAllDataGrouped()
        {
            // Arrange
            var tanker = SetupForSummaryTests();

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate);

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count(), "Should have 3 months of data.");

            var jan = summary.MonthlySummaries.Single(m => m.Month == "01/2024");
            Assert.AreEqual(110m, jan.MonthTotal); // 100 (tank) + 10 (root)
            Assert.AreEqual(1, jan.Tanks.Count());
            Assert.AreEqual(1, jan.RootDeposits.Count());

            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(270m, feb.MonthTotal); // 200 + 50 (tanks) + 20 (root)
            Assert.AreEqual(2, feb.Tanks.Count());
            Assert.AreEqual(1, feb.RootDeposits.Count());

            var mar = summary.MonthlySummaries.Single(m => m.Month == "03/2024");
            Assert.AreEqual(330m, mar.MonthTotal); // 300 (tank) + 30 (root)
            Assert.AreEqual(1, mar.Tanks.Count());
            Assert.AreEqual(1, mar.RootDeposits.Count());
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterByStartDate_ReturnsCorrectSubset()
        {
            // Arrange
            var tanker = SetupForSummaryTests();
            var startDate = new DateTime(2024, 2, 1);

            // Act
            var summary = tanker.BuildMonthlySummary(startDate, defaultEndDate);

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count(), "Should still list all months with root deposits.");

            var jan = summary.MonthlySummaries.Single(m => m.Month == "01/2024");
            Assert.AreEqual(10m, jan.MonthTotal); // 0 (filtered tanks) + 10 (root)
            Assert.AreEqual(0, jan.Tanks.Count(), "Jan tanks should be filtered out by date.");
            Assert.AreEqual(1, jan.RootDeposits.Count());

            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(270m, feb.MonthTotal); // 250 (tanks) + 20 (root)
            Assert.AreEqual(2, feb.Tanks.Count());
            Assert.AreEqual(1, feb.RootDeposits.Count());

            var mar = summary.MonthlySummaries.Single(m => m.Month == "03/2024");
            Assert.AreEqual(330m, mar.MonthTotal); // 300 (tank) + 30 (root)
            Assert.AreEqual(1, mar.Tanks.Count());
            Assert.AreEqual(1, mar.RootDeposits.Count());
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterByEndDate_ReturnsCorrectSubset()
        {
            // Arrange
            var tanker = SetupForSummaryTests();
            var endDate = new DateTime(2024, 2, 28);

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, endDate: endDate);

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count(), "Should still list all months with root deposits.");

            var jan = summary.MonthlySummaries.Single(m => m.Month == "01/2024");
            Assert.AreEqual(110m, jan.MonthTotal); // 100 (tank) + 10 (root)
            Assert.AreEqual(1, jan.Tanks.Count());
            Assert.AreEqual(1, jan.RootDeposits.Count());

            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(270m, feb.MonthTotal); // 250 (tanks) + 20 (root)
            Assert.AreEqual(2, feb.Tanks.Count());
            Assert.AreEqual(1, feb.RootDeposits.Count());

            var mar = summary.MonthlySummaries.Single(m => m.Month == "03/2024");
            Assert.AreEqual(30m, mar.MonthTotal); // 0 (filtered tanks) + 30 (root)
            Assert.AreEqual(0, mar.Tanks.Count(), "Mar tanks should be filtered out by date.");
            Assert.AreEqual(1, mar.RootDeposits.Count());
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterByDateRange_ReturnsCorrectSubset()
        {
            // Arrange
            var tanker = SetupForSummaryTests();
            var startDate = new DateTime(2024, 2, 1);
            var endDate = new DateTime(2024, 2, 28);

            // Act
            var summary = tanker.BuildMonthlySummary(startDate: startDate, endDate: endDate);

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count(), "Should still list all months with root deposits.");

            var jan = summary.MonthlySummaries.Single(m => m.Month == "01/2024");
            Assert.AreEqual(10m, jan.MonthTotal); // 0 (tanks) + 10 (root)
            Assert.AreEqual(0, jan.Tanks.Count());

            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(270m, feb.MonthTotal); // 250 (tanks) + 20 (root)
            Assert.AreEqual(2, feb.Tanks.Count());

            var mar = summary.MonthlySummaries.Single(m => m.Month == "03/2024");
            Assert.AreEqual(30m, mar.MonthTotal); // 0 (tanks) + 30 (root)
            Assert.AreEqual(0, mar.Tanks.Count());
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterByName_ReturnsCorrectTanks()
        {
            // Arrange
            var tanker = SetupForSummaryTests();

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate, name: "alpha"); // Test case-insensitivity

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count());

            var jan = summary.MonthlySummaries.Single(m => m.Month == "01/2024");
            Assert.AreEqual(110m, jan.MonthTotal); // 100 ("Alpha Jan Tank") + 10 (root)
            Assert.AreEqual(1, jan.Tanks.Count());
            Assert.AreEqual("Alpha Jan Tank", jan.Tanks.Single().Name);

            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(20m, feb.MonthTotal); // 0 (filtered tanks) + 20 (root)
            Assert.AreEqual(0, feb.Tanks.Count(), "Feb tanks should not match 'alpha'.");

            var mar = summary.MonthlySummaries.Single(m => m.Month == "03/2024");
            Assert.AreEqual(330m, mar.MonthTotal); // 300 ("Alpha Mar Tank") + 30 (root)
            Assert.AreEqual(1, mar.Tanks.Count());
            Assert.AreEqual("Alpha Mar Tank", mar.Tanks.Single().Name);
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterByStatus_ReturnsCorrectTanks()
        {
            // Arrange
            var tanker = SetupForSummaryTests(); // All tanks are 'TankReady' in a TankerPending

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate, status: FilterContainerType.ALL); // Test case-insensitivity

            // Assert: Should be the same as the no-filter test, since all tanks match
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count());
            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(2, feb.Tanks.Count());
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterByNonMatchingStatus_ReturnsNoTanks()
        {
            // Arrange
            var tanker = SetupForSummaryTests();

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate, status: FilterContainerType.ALL);

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count(), "All months should still appear due to root deposits.");

            // Check that no tanks are returned in any month
            Assert.IsTrue(summary.MonthlySummaries.All(m => m.Tanks.Count() == 0));
            // Check that month totals are only the root deposit amounts
            Assert.AreEqual(10m, summary.MonthlySummaries.Single(m => m.Month == "01/2024").MonthTotal);
            Assert.AreEqual(20m, summary.MonthlySummaries.Single(m => m.Month == "02/2024").MonthTotal);
            Assert.AreEqual(30m, summary.MonthlySummaries.Single(m => m.Month == "03/2024").MonthTotal);
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterByColor_ReturnsCorrectTanks()
        {
            // Arrange
            var tanker = SetupForSummaryTests();

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate, color: "#FFFF00"); // Yellow, test case-sensitivity of hex

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count());

            var jan = summary.MonthlySummaries.Single(m => m.Month == "01/2024");
            Assert.AreEqual(110m, jan.MonthTotal); // 100 (yellow tank) + 10 (root)
            Assert.AreEqual(1, jan.Tanks.Count());
            //Assert.AreEqual("#FFFF00", jan.Tanks.Single().Type); // Oops, this should be Color not Type. The provided TankDetail class doesn't have Color. Assuming the filter works as intended.

            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(70m, feb.MonthTotal); // 50 (yellow tank) + 20 (root)
            Assert.AreEqual(1, feb.Tanks.Count());
            Assert.AreEqual("Gamma Feb Tank", feb.Tanks.Single().Name);

            var mar = summary.MonthlySummaries.Single(m => m.Month == "03/2024");
            Assert.AreEqual(30m, mar.MonthTotal); // 0 (filtered tanks) + 30 (root)
            Assert.AreEqual(0, mar.Tanks.Count());
        }

        [TestMethod]
        public void BuildMonthlySummary_FilterWithNoResults_ReturnsOnlyRootDeposits()
        {
            // Arrange
            var tanker = SetupForSummaryTests();

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate, name: "NonExistentName");

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count(), "All months with root deposits should be present.");

            // Verify all monthly summaries have 0 tanks
            Assert.IsTrue(summary.MonthlySummaries.All(m => !m.Tanks.Any()));

            // Verify month totals equal the root deposit amounts
            Assert.AreEqual(10m, summary.MonthlySummaries.First(m => m.Month == "01/2024").MonthTotal);
            Assert.AreEqual(20m, summary.MonthlySummaries.First(m => m.Month == "02/2024").MonthTotal);
            Assert.AreEqual(30m, summary.MonthlySummaries.First(m => m.Month == "03/2024").MonthTotal);
        }

        [TestMethod]
        public void BuildMonthlySummary_CombinedFilters_ReturnsSpecificResult()
        {
            // Arrange
            var tanker = SetupForSummaryTests();
            var startDate = new DateTime(2024, 2, 1);
            var endDate = new DateTime(2024, 2, 28);

            // Act: Find yellow tanks in February
            var summary = tanker.BuildMonthlySummary(
                startDate: startDate,
                endDate: endDate,
                name: "Gamma",
                status: FilterContainerType.ALL,
                color: "#FFFF00");

            // Assert
            Assert.AreEqual(710m, summary.TankerTotalAmount);
            Assert.AreEqual(3, summary.MonthlySummaries.Count());

            var jan = summary.MonthlySummaries.Single(m => m.Month == "01/2024");
            Assert.AreEqual(10m, jan.MonthTotal); // 0 tanks + 10 root
            Assert.AreEqual(0, jan.Tanks.Count());

            var feb = summary.MonthlySummaries.Single(m => m.Month == "02/2024");
            Assert.AreEqual(70m, feb.MonthTotal); // 50 (Gamma tank) + 20 (root)
            Assert.AreEqual(1, feb.Tanks.Count());
            Assert.AreEqual("Gamma Feb Tank", feb.Tanks.Single().Name);

            var mar = summary.MonthlySummaries.Single(m => m.Month == "03/2024");
            Assert.AreEqual(30m, mar.MonthTotal); // 0 tanks + 30 root
            Assert.AreEqual(0, mar.Tanks.Count());
        }

        [TestMethod]
        public void BuildMonthlySummary_WithOnlyRootDeposits_GroupsCorrectly()
        {
            // Arrange
            var (liquid, domain, itIsThePresent) = SetupTestEnvironment();
            var source = liquid.Source;
            var dateJan = new DateTime(2023, 1, 15);
            var dateFeb = new DateTime(2023, 2, 10);

            var depositJan1 = CreateAndConfirmDeposit(source, domain, dateJan, 10m);
            var depositJan2 = CreateAndConfirmDeposit(source, domain, dateJan, 20m);
            var depositFeb1 = CreateAndConfirmDeposit(source, domain, dateFeb, 50m);
            var depositFeb2 = CreateAndConfirmDeposit(source, domain, dateFeb, 5m);

            int jarVersion = source.NextJarVersion();
            var tankFeb = source.Jar.CreateTank(itIsThePresent, dateFeb, source.NextTankId(), "TankFeb", "Desc", jarVersion, new List<int> { depositFeb2.Id });

            var tanker = source.CreateTanker(itIsThePresent, DateTime.Now, source.NextTankerId(), "DepositsOnlyTanker", "Desc", new List<int>() { tankFeb.Id });
            tanker.AddDepositsByIds(new List<int> { depositJan1.Id, depositJan2.Id, depositFeb1.Id });
            tanker = source.FindTanker(tanker.Id) as TankerPending;

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate);

            // Assert
            Assert.IsNotNull(summary);
            Assert.AreEqual(85m, summary.TankerTotalAmount);
            Assert.AreEqual(2, summary.MonthlySummaries.Count(), "Should have two monthly summaries.");

            // January Summary
            var janSummary = summary.MonthlySummaries.FirstOrDefault(m => m.Month == "01/2023");
            Assert.IsNotNull(janSummary, "January summary not found.");
            Assert.AreEqual(30m, janSummary.MonthTotal);
            Assert.AreEqual(0, janSummary.Tanks.Count(), "Should have no tanks in Jan summary.");
            Assert.AreEqual(2, janSummary.RootDeposits.Count(), "Should have two root deposits in Jan summary.");

            // February Summary
            var febSummary = summary.MonthlySummaries.FirstOrDefault(m => m.Month == "02/2023");
            Assert.IsNotNull(febSummary, "February summary not found.");
            Assert.AreEqual(55m, febSummary.MonthTotal);
            Assert.AreEqual(1, febSummary.Tanks.Count(), "Should have 1 tanks in Feb summary.");
            Assert.AreEqual(1, febSummary.RootDeposits.Count(), "Should have one root deposit in Feb summary.");
        }

        [TestMethod]
        public void BuildMonthlySummary_WithMixedTanksAndDeposits_GroupsCorrectly()
        {
            // Arrange
            var (liquid, domain, itIsThePresent) = SetupTestEnvironment();
            var source = liquid.Source;
            var dateJan = new DateTime(2023, 1, 15);
            var dateFeb = new DateTime(2023, 2, 10);

            // Create all deposits that will eventually be moved into tanks.
            var depositForTankJan1 = CreateAndConfirmDeposit(source, domain, dateJan, 100m);
            var depositForTankJan2 = CreateAndConfirmDeposit(source, domain, dateJan, 50m);
            var depositForTankFeb1 = CreateAndConfirmDeposit(source, domain, dateFeb, 200m);
            var depositForTankFeb2 = CreateAndConfirmDeposit(source, domain, dateFeb, 100m);

            // Create the first tank. This replaces the Jar in the source.
            int jarVersion = source.NextJarVersion();
            var tankJan = source.Jar.CreateTank(itIsThePresent, dateJan, source.NextTankId(), "TankJan", "Desc", jarVersion, new List<int> { depositForTankJan1.Id, depositForTankJan2.Id });

            // Create the second tank from the new Jar, which contains the remaining deposits.
            jarVersion = source.NextJarVersion();
            var tankFeb = source.Jar.CreateTank(itIsThePresent, dateFeb, source.NextTankId(), "TankFeb", "Desc", jarVersion, new List<int> { depositForTankFeb1.Id, depositForTankFeb2.Id });

            // Create deposits that will go into the tanker's root tank, using the final Jar.
            var rootDepositJan = CreateAndConfirmDeposit(source, domain, dateJan, 25m);
            var rootDepositFeb = CreateAndConfirmDeposit(source, domain, dateFeb, 75m);

            // Create the tanker, including the tanks we created.
            var tanker = source.CreateTanker(itIsThePresent, DateTime.Now, source.NextTankerId(), "MixedTanker", "Desc", new List<int> { tankJan.Id, tankFeb.Id });

            // Add the root deposits to the tanker.
            tanker.AddDepositsByIds(new List<int> { rootDepositJan.Id, rootDepositFeb.Id });
            tanker = source.FindTanker(tanker.Id) as TankerPending;

            // Act
            var summary = tanker.BuildMonthlySummary(defaultStartDate, defaultEndDate);

            // Assert
            Assert.IsNotNull(summary);
            // Total = (100+50 for tankJan) + (200+100 for tankFeb) + (25 for rootJan) + (75 for rootFeb) = 550
            Assert.AreEqual(550m, summary.TankerTotalAmount);
            Assert.AreEqual(2, summary.MonthlySummaries.Count(), "Should have two monthly summaries.");

            // January Summary
            var janSummary = summary.MonthlySummaries.FirstOrDefault(m => m.Month == "01/2023");
            Assert.IsNotNull(janSummary, "January summary not found.");
            Assert.AreEqual(175m, janSummary.MonthTotal, "Incorrect total for January."); // 150 from tank + 25 from root
            Assert.AreEqual(1, janSummary.Tanks.Count(), "Should have one tank in Jan summary.");
            Assert.AreEqual(1, janSummary.RootDeposits.Count(), "Should have one root deposit in Jan summary.");
            Assert.AreEqual(tankJan.Id, janSummary.Tanks.Single().Id);
            Assert.AreEqual(rootDepositJan.Id, janSummary.RootDeposits.Single().Id);

            // February Summary
            var febSummary = summary.MonthlySummaries.FirstOrDefault(m => m.Month == "02/2023");
            Assert.IsNotNull(febSummary, "February summary not found.");
            Assert.AreEqual(375m, febSummary.MonthTotal, "Incorrect total for February."); // 300 from tank + 75 from root
            Assert.AreEqual(1, febSummary.Tanks.Count(), "Should have one tank in Feb summary.");
            Assert.AreEqual(1, febSummary.RootDeposits.Count(), "Should have one root deposit in Feb summary.");
            Assert.AreEqual(tankFeb.Id, febSummary.Tanks.Single().Id);
            Assert.AreEqual(rootDepositFeb.Id, febSummary.RootDeposits.Single().Id);
        }

        #endregion
        [TestMethod]
        public void TestTankerCreation()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Amount == 0);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Amount == 1);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Amount == 1);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Amount == 1);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);
            Assert.IsTrue(source.Amount == 2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);

            //Create Tanker

            int[] tankIds = { tank1.Id, tank2.Id, tank3.Id, tank4.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker","Tanker description", tankIds);

            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Amount == 4);
        }



        [TestMethod]
        public void TestTankerStartDonwloadAndDispath()
        {
            PaymentManager.NodeExplorerUrl = "http://**********:30781";
            PaymentManager.NodeExplorerClient = new NodeExplorerClient(PaymentManager.NodeExplorerUrl);
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1qexq9e80au56xn4shepkpcpzlxyj7vtfn5dy07v", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Amount == 0);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Amount == 1);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Amount == 1);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            //Create Tanker
            int[] tankIds = { tank1.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description", tankIds);

            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Amount == 1);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquid.Source.Amount == 1);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            tankerSeal.Disburden(itIsThePresent);

            //KAFKA CODE
            Assert.IsTrue(liquid.Source.Tanks.Count() == 2);
            Assert.IsTrue(liquid.Source.Tankers.Count() == 1);
            tankerSeal.Dispatched(now);
            Assert.IsTrue(liquid.Source.Tanks.Count() == 0);
            Assert.IsTrue(liquid.Source.Tankers.Count() == 0);
        }


        [TestMethod]
        public void TestMoveTankExistTanker()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");

            liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");
            var paymentEngineDock = liquid.ParentFlow.FindPaymentEngineDock(domain);
            Assert.IsTrue(paymentEngineDock != null);

            Assert.IsTrue(liquid.Source.Jar.PreviousLegacyJar == null);

            var deposit1 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", liquid.Source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1M);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 0);

            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1M);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 1M);

            var deposit2 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address2", liquid.Source.NextDepositId(), $"1{txid}", 12, 31, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address3", liquid.Source.NextDepositId(), $"1{txid}", 13, 11, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address4", liquid.Source.NextDepositId(), $"1{txid}", 14, 15, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address5", liquid.Source.NextDepositId(), $"1{txid}", 16, 16, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address6", liquid.Source.NextDepositId(), $"1{txid}", 18, 17, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address7", liquid.Source.NextDepositId(), $"1{txid}", 19, 91, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7M);

            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit2);

            int tankId = liquid.Source.NextTankId();
            int jarVerion = liquid.Source.NextJarVersion();

            var tank1 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Jar.Amount == 5M);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();
            Assert.IsTrue(tank1.Version == 1);

            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit3);
            var tank2 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para auto", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            
            //Create Tanker

            int[] tankIds = { tank1.Id };
            var tanker = liquid.Source.CreateTanker(itIsThePresent, now, liquid.Source.NextTankerId(), "MiTanker","Tanker description", tankIds);

            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Amount == 2m);
            liquid.Source.Jar.Source.MoveToTanker(itIsThePresent, now, tanker.Id, new List<int>() { tank2.Id });
            tanker = liquid.Source.FindTanker(tanker.Id) as TankerPending;
            Assert.IsTrue(tanker.Amount == 3m);
            Assert.IsTrue(tanker.Tanks.Count() == 3);

        }

        [TestMethod]
        public void TestTankerStartDonwloadAndPartialDispath()
        {
            PaymentManager.NodeExplorerUrl = "http://**********:30781";
            PaymentManager.NodeExplorerClient = new NodeExplorerClient(PaymentManager.NodeExplorerUrl);
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquid.Source.AddXpub(new Xpub("dasfsdfdsfsd"));


            LiquidFlow.ClearInstance();
            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 55, "tb1qn73twe8nkjwss93qdhuadkp0hylz0dnpgh897p", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit2 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 55, "tb1q8a4vdfzz2xuh9j8yklfqk09h8fk64p8ladnkdf", 1M, 100000M, "USD", 100, 1, 1, domain);
            //var deposit1 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 55, "tb1q8a4vdfzz2xuh9j8yklfqk09h8fk64p8ladnkdf", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Amount == 0);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit2);
            Assert.IsTrue(liquid.Source.Amount == 2);

            int tankId = liquid.Source.NextTankId();
            int jarVerion = liquid.Source.NextJarVersion();

            var tank1 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Amount == 2);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            //Create Tanker
            int[] tankIds = { tank1.Id };
            var tanker = liquid.Source.CreateTanker(itIsThePresent, now, liquid.Source.NextTankerId(), "MiTanker", "Tanker description", tankIds);

            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Amount == 2);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquid.Source.Amount == 2);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            tankerSeal.Disburden(itIsThePresent);

            //KAFKA CODE
            int nextTankerId = liquid.Source.NextTankerId();
            Assert.IsTrue(liquid.Source.Tanks.Count() == 2);
            Assert.IsTrue(liquid.Source.Tankers.Count() == 1);
            tankerSeal.PartialDispatched(nextTankerId, now, new List<int> { deposit1.Id });
            Assert.IsTrue(liquid.Source.Tanks.Count() == 2);
            Assert.IsTrue(liquid.Source.Tankers.Count() == 1);
        }

        [TestMethod]
        public void TestMoveDepositToExistTanker()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");


            Assert.IsTrue(source.Jar.PreviousLegacyJar == null);

            var deposit1 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);
            Assert.IsTrue(source.Jar.AvailableAmount == 0);

            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);
            Assert.IsTrue(source.Jar.AvailableAmount == 1M);

            var deposit2 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address2", source.NextDepositId(), $"1{txid}", 12, 31, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address3", source.NextDepositId(), $"1{txid}", 13, 11, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address4", source.NextDepositId(), $"1{txid}", 14, 15, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address5", source.NextDepositId(), $"1{txid}", 16, 16, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address6", source.NextDepositId(), $"1{txid}", 18, 17, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address7", source.NextDepositId(), $"1{txid}", 19, 91, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit2);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 5M);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit3);
            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para auto", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank1.Version == 1);

            //Create Tanker

            int[] tankIds = { tank1.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description", tankIds);

            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Amount == 2m);
            source.MoveToTanker(itIsThePresent, now, tanker.Id, new List<int>() { tank2.Id });
            tanker = source.FindTanker(tanker.Id) as TankerPending;
            Assert.IsTrue(tanker.Amount == 3m);
            Assert.IsTrue(tanker.Tanks.Count() == 3);
            jarVerion = source.NextJarVersion();
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit4);
            jarVerion = source.NextJarVersion();
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit5);
            jarVerion = source.NextJarVersion();
            IEnumerable<int> deposits = new List<int> { deposit4.Id, deposit5.Id };
            //bool itIsThePresent, DateTime now, int vesion, int tankerId, IEnumerable< int > selectedDeposits
            source.Jar.MoveToTanker(itIsThePresent, now, jarVerion, tanker.Id, deposits);
            tanker = source.FindTanker(tanker.Id) as TankerPending;
            Assert.IsTrue(source.Jar.Amount == 2M);
            Assert.IsTrue(tanker.Amount == 5m);
        }


        [TestMethod]
        public void TestMoveDepositToExistTankerAndDifConsecutiveTankIdFromRoot()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            Jar activeJar = null;

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            activeJar = source.Jar;

            Assert.IsTrue(activeJar.PreviousLegacyJar == null);

            var deposit1 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address1", source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(activeJar.Amount == 1M);
            Assert.IsTrue(activeJar.AvailableAmount == 0);

            activeJar.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(activeJar.Amount == 1M);
            Assert.IsTrue(activeJar.AvailableAmount == 1M);

            var deposit2 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address2", source.NextDepositId(), $"1{txid}", 12, 31, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address3", source.NextDepositId(), $"1{txid}", 13, 11, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address4", source.NextDepositId(), $"1{txid}", 14, 15, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address5", source.NextDepositId(), $"1{txid}", 16, 16, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address6", source.NextDepositId(), $"1{txid}", 18, 17, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address7", source.NextDepositId(), $"1{txid}", 19, 91, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(activeJar.Amount == 7M);

            activeJar.ConfirmDeposit(itIsThePresent, now, deposit2);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = activeJar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(activeJar.Amount == 5M);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
          
            //Create Tanker

            int[] tankIds = { tank1.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description", tankIds);
            jarVerion = source.NextJarVersion();
            tankId = source.NextTankId();
            var tank3 = activeJar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para auto", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1.Version == activeJar.PreviousLegacyJar.Version);
            Assert.IsTrue(tanker != null);
           
            Assert.IsFalse(tanker.Tanks.Any(t=>t.Id == tank3.Id));

        }

        [TestMethod]
        public void TestMoveToTankWithSomeDepositsAlreadyAssigned()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            Jar activeJar = source.Jar;

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            // Create and confirm deposits
            var deposit1 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address1", source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit2 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address2", source.NextDepositId(), $"1{txid}", 2, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address3", source.NextDepositId(), $"1{txid}", 3, 3, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);

            activeJar.ConfirmDeposit(itIsThePresent, now, deposit1);
            activeJar.ConfirmDeposit(itIsThePresent, now, deposit2);
            activeJar.ConfirmDeposit(itIsThePresent, now, deposit3);

            // Create a tank and assign deposit1 to it
            int tankId1 = source.NextTankId();
            int jarVersion1 = source.NextJarVersion();
            var tank1 = activeJar.CreateTank(itIsThePresent, now, tankId1, "Tank1", "First tank", jarVersion1, new List<int> { deposit1.Id });

            Assert.IsTrue(tank1.ExplandedDeposits.Any(d => d.Id == deposit1.Id));
            Assert.IsFalse(tank1.ExplandedDeposits.Any(d => d.Id == deposit2.Id));
            Assert.IsFalse(tank1.ExplandedDeposits.Any(d => d.Id == deposit3.Id));

            // Move deposit2 and deposit3 to a new tank
            int tankId2 = source.NextTankId();
            int jarVersion2 = source.NextJarVersion();
            var tank2 = activeJar.CreateTank(itIsThePresent, now, tankId2, "Tank2", "Second tank", jarVersion2, new List<int> { deposit2.Id, deposit3.Id });

            Assert.IsTrue(tank2.ExplandedDeposits.Any(d => d.Id == deposit2.Id));
            Assert.IsTrue(tank2.ExplandedDeposits.Any(d => d.Id == deposit3.Id));
            Assert.IsFalse(tank2.ExplandedDeposits.Any(d => d.Id == deposit1.Id));

            tank1.MoveToTanks(new List<int>{ tank2.Id });
        }

        [TestMethod]
        public void TestMoveToTanksWithSomeDepositsAlreadyAssigned()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            // Create and confirm deposits
            var deposit1 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit2 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address2", source.NextDepositId(), $"1{txid}", 2, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address3", source.NextDepositId(), $"1{txid}", 3, 3, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);

            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit1);
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit2);
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit3);

            // Create a tank and assign deposit1 to it
            int tankId1 = source.NextTankId();
            int jarVersion1 = source.NextJarVersion();
            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId1, "Tank1", "First tank", jarVersion1, new List<int> { deposit1.Id });

            Assert.IsTrue(tank1.ExplandedDeposits.Any(d => d.Id == deposit1.Id));
            Assert.IsFalse(tank1.ExplandedDeposits.Any(d => d.Id == deposit2.Id));
            Assert.IsFalse(tank1.ExplandedDeposits.Any(d => d.Id == deposit3.Id));

            // Move deposit2 and deposit3 to a new tank
            int tankId2 = source.NextTankId();
            int jarVersion2 = source.NextJarVersion();
            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId2, "Tank2", "Second tank", jarVersion2, new List<int> { deposit2.Id, deposit3.Id });

            Assert.IsFalse(tank2.ExplandedDeposits.Any(d => d.Id == deposit1.Id));
            Assert.IsTrue(tank2.ExplandedDeposits.Any(d => d.Id == deposit2.Id));
            Assert.IsTrue(tank2.ExplandedDeposits.Any(d => d.Id == deposit3.Id));

            // create another deposits and a tank 
            var deposit4 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address4", source.NextDepositId(), $"1{txid}", 4, 4, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit4);

            int tankId3 = source.NextTankId();
            int jarVersion3 = source.NextJarVersion();
            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId3, "Tank3", "Third tank", jarVersion3, new List<int> { deposit4.Id });

            tank1.MoveToTanks(new List<int> { tank2.Id, tank3.Id });
            Assert.IsTrue(tank1.ExplandedDeposits.Count() == 4);

            // create a tanker
            int[] tankIds = { tank1.Id, tank2.Id, tank3.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description", tankIds);

            //another new deposit and tank 
            var deposit5 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address5", source.NextDepositId(), $"1{txid}", 5, 5, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit5);
            int tankId4 = source.NextTankId();
            int jarVersion4 = source.NextJarVersion();
            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId4, "Tank4", "Fourth tank", jarVersion4, new List<int> { deposit5.Id });

            // Trying to move tank3 to tanker should throw an exception, cause it has deposits already assigned to a tanker
            Assert.Throws<GameEngineException>(() =>
            {
                tank1.MoveToTanks(new List<int> { tank3.Id, tank4.Id });
            });

            Assert.IsTrue(liquid.Source.IsTankInUse(tank3));
            Assert.IsFalse(liquid.Source.IsTankInUse(tank4));

            // Create a new tanker with tank3 and tank4
            Assert.Throws<GameEngineException>(() =>
            {
                source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "NewTanker", "New tanker description", new int[] { tank3.Id, tank4.Id });
            });
            
            int[] newTankIds = { tank4.Id };
            var tanker2 = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "NewTanker", "New tanker description", newTankIds);

            var deposit6 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address6", source.NextDepositId(), $"1{txid}", 6, 6, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit6);

            int tankId5 = source.NextTankId();
            int jarVersion5 = source.NextJarVersion();
            var tank5 = source.Jar.CreateTank(itIsThePresent, now, tankId5, "Tank5", "Fifth tank", jarVersion5, new List<int> { deposit6.Id });

            var deposit7 = source.Jar.CreateDraftDeposit(itIsThePresent, now, "address7", source.NextDepositId(), $"1{txid}", 7, 7, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            source.Jar.ConfirmDeposit(itIsThePresent, now, deposit7);

            int tankId6 = source.NextTankId();
            int jarVersion6 = source.NextJarVersion();
            var tank6 = source.Jar.CreateTank(itIsThePresent, now, tankId6, "Tank6", "Sixth tank", jarVersion6, new List<int> { deposit7.Id });

            tank6.MoveToTanks(new List<int> { tank5.Id });
            Assert.IsTrue(tank6.ExplandedDeposits.Count() == 2);
            Assert.IsTrue(tank5.ExplandedDeposits.Count() == 1);
        }

        [TestMethod]
        public void TestTankerChangeColor()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);

            //Create Tanker

            int[] tankIds = { tank1.Id, tank2.Id, tank3.Id, tank4.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description", tankIds);
            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.ContainerColor == string.Empty);
            Assert.IsTrue(tanker.Amount == 4M);
            var yellow = "Yellow";
            tanker.ChangeColor(yellow);
            Assert.IsTrue(tanker.ContainerColor == yellow);
        }

        [TestMethod]
        public void TestTankerCreationWithoutTanksIds()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);

            //Create Tanker

            int[] tankIds = { tank1.Id, tank2.Id, tank3.Id, tank4.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description");

            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Amount == 0);
            Assert.IsTrue(tanker.Tanks.Count() == 1);
        }

        [TestMethod]
        public void TestTankerCreationWithoutTanksIdsAndSetGoalAmount()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);

            //Create Tanker

            int[] tankIds = { tank1.Id, tank2.Id, tank3.Id, tank4.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description");
            Assert.IsTrue(tanker.GoalAmount.IsEmpty);
            tanker.AddGoalAmount(10M,"ETH");
            var newTanker = source.FindTanker(tanker.Id);
            Assert.IsTrue(newTanker != null);
            Assert.IsTrue(newTanker.Amount == 0);
            Assert.IsTrue(newTanker.Tanks.Count() == 1);
            Assert.IsTrue(newTanker.GoalAmount.GetAmount() == 10M);
            newTanker.ClearGoalAmunt();
            Assert.IsFalse(newTanker.GoalAmount.IsEmpty);
        }

        [TestMethod]
        public void TestTanker()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now.AddDays(2), source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);

            //Create Tanker

            int[] tankIds = { tank1.Id, tank2.Id, tank3.Id, tank4.Id };
            var tanker = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker", "Tanker description");
            source.MoveToTanker(false,now,tanker.Id, new List<int> { tank1.Id, tank2.Id });
            var tanker2 = source.CreateTanker(itIsThePresent, now, source.NextTankerId(), "MiTanker2", "Tanker description2");
            source.MoveToTanker(false,now, tanker2.Id, new List<int> { tank3.Id, tank4.Id });

            var tankers = source.BuildTankerWithDepositsBetween(now.AddDays(-1), now.AddDays(-1),TankerStatus.ALL,"","");
            Assert.AreEqual(0, tankers.Count);

            tankers = source.BuildTankerWithDepositsBetween(now, now.AddDays(1), TankerStatus.ALL, "", "");
            Assert.AreEqual(2, tankers.Count);

            tankers = source.BuildTankerWithDepositsBetween(now.AddDays(2), now.AddDays(2), TankerStatus.ALL, "", "");
            Assert.AreEqual(1, tankers.Count);

            tankers = source.BuildTankerWithDepositsFrom(now.AddDays(2), TankerStatus.ALL, "", "");

            Assert.AreEqual(1, tankers.Count);

            tankers = source.BuildTankerWithDepositsUpTo(now.AddDays(2),TankerStatus.ALL,"", "");

            Assert.AreEqual(2, tankers.Count);
        }

        internal class TestActor : Puppeteer.EventSourcing.Actor
        {
            internal TestActor() : base(nameof(TestActor))
            {
            }
        }

        [TestMethod]    
        public void TestTankerArchive()
        {
            CurrenciesTest.AddCurrencies();
            ExecutionContext.Current.SetContext(DateTime.Now, false, new TestActor());

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now.AddDays(2), source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == source.Jar.PreviousLegacyJar.Version);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == source.Jar.PreviousLegacyJar.Version);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == source.Jar.PreviousLegacyJar.Version);

            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == source.Jar.PreviousLegacyJar.Version);

            //Create Tanker

            int[] tankIds = { tank1.Id, tank2.Id, tank3.Id, tank4.Id };
            int tankerId = source.NextTankerId();
            var tanker = source.CreateTanker(itIsThePresent, now, tankerId, "MiTanker", "Tanker description", tankIds);
            Assert.IsTrue(source.FindTanker(tankerId) is TankerPending);

            var tankerSealed = tanker.Sealed();
            Assert.IsTrue(source.FindTanker(tankerId) is TankerSealed);

            tankerSealed.Disburden(itIsThePresent);
            tankerSealed.Dispatched(now);
            Assert.IsTrue(source.FindTanker(tankerId) is TankerDispatched);

            var tankerDispatched = source.FindTanker(tankerId) as TankerDispatched;

            Assert.IsTrue(source.Tankers.Count() == 1);
            Assert.IsTrue(source.ObsoleteTankers.Count() == 0);
            tankerDispatched.Archive();
            Assert.IsTrue(source.Tankers.Count() == 0);
            Assert.IsTrue(source.ObsoleteTankers.Count() == 1);


            Assert.IsTrue(source.FindTanker(tankerId) is TankerArchived);
        }


        [TestMethod]
        public void TestTankerWithDeposits()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == source.Jar.PreviousLegacyJar.Version);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);
            source.ConfirmDeposit(itIsThePresent, now, deposit4);
            source.ConfirmDeposit(itIsThePresent, now, deposit5);

            jarVerion = source.NextJarVersion();

            //Create Tanker
            List<int> deposits = new List<int> { deposit3.Id, deposit4.Id, deposit5.Id };
            TankerPending tanker = source.CreateTankerWithDeposits(itIsThePresent, now, jarVerion, source.NextTankerId(), "MiTanker", "Tanker description", deposits);
            Assert.IsTrue(tanker.Amount == 3M);
            Assert.IsTrue(tanker.Tanks.Count() ==1);
            Assert.IsFalse(source.Jar.Deposits.Contains(deposit3));
            Assert.IsFalse(source.Jar.Deposits.Contains(deposit4));
            Assert.IsFalse(source.Jar.Deposits.Contains(deposit5));

        }


    }
}
