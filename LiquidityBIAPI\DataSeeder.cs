﻿using GamesEngine.Business.Liquidity.Persistence;
using GamesEngine.Settings;
using System.Diagnostics;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;

namespace LiquidityBIAPI
{
    public static class DataSeeder
    {
        public static void SeedInitialData(IOlapRepository olapStorage, ISearchStorage searchStorage, string currencyCode)
        {
            if (olapStorage == null && searchStorage == null)
            {
                Debug.WriteLine("DataSeeder: Both OLAP and Search storage are null. No data will be seeded.");
                return;
            }
            try
            {
                var allSeededDepositIds = new List<long>();
                var depositsToCreate = GetDeposits();
                foreach (var deposit in depositsToCreate)
                {
                    if (olapStorage != null)
                    {
                        olapStorage.CreateDeposit(currencyCode, deposit);
                    }
                    if (searchStorage != null)
                    {
                        searchStorage.CreateDeposit(currencyCode, deposit);
                    }
                    allSeededDepositIds.Add(deposit.Id);
                }

                SeedJarsAndDetails(olapStorage, searchStorage, currencyCode, allSeededDepositIds);
                SeedTanksAndDetails(olapStorage, searchStorage, currencyCode, depositsToCreate);
                SeedTankersAndDetails(olapStorage, searchStorage, currencyCode, depositsToCreate);

                SeedWithdrawalsAndDetails(olapStorage, searchStorage, currencyCode);

                SeedInvoicePayments(searchStorage, currencyCode);

                Debug.WriteLine("DataSeeder: Initial data seeding process completed successfully.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DataSeeder: An error occurred during initial data seeding: {ex.Message}", ex);
                throw;
            }
        }

        private static List<Deposit> GetDeposits()
        {
            var baseDate = DateTime.Now.AddDays(-60);
            int initialID = 100000;
            return new List<Deposit>
            {
                // Group 1: AccountNumber "ACC123"
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP001", Amount = 150.75m, Rate = 62500.50m, Date = baseDate.AddDays(5).AddHours(10), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "tb1q9t2n99900fr4xmpk0nmgnsmvjj8v9xjz7g9qjw", Created = DateTime.Now.AddDays(-55), State = "Completed", ExternalId = "EXT123456789" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP002", Amount = 300.00m, Rate = 63100.20m, Date = baseDate.AddDays(10).AddHours(11), StoreId = 2, AccountNumber = "ACC123", DomainId = 2, Address = "tb1q1234567890123456789012345678901234567890", Created = DateTime.Now.AddDays(-50), State = "Completed", ExternalId = "EXT987654321" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP003", Amount = 75.50m,  Rate = 64250.00m, Date = baseDate.AddDays(15).AddHours(9),  StoreId = 2, AccountNumber = "ACC123", DomainId = 2, Address = "tb1q0s4h0mkrggr5t20nmgnsmvjj8v9xjz7vxyz00", Created = DateTime.Now.AddDays(-45), State = "Completed", ExternalId = "EXT555555555" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP015", Amount = 25.00m,  Rate = 70500.75m, Date = DateTime.Now.AddDays(-2).AddHours(10), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "tb1qabcdef0123456789abcdef0123456789abcdef01", Created = DateTime.Now.AddDays(-2), State = "Completed", ExternalId = "EXT444444444" },

                // Group 2: AccountNumber "ACC456"
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP004", Amount = 1250.00m,Rate = 62800.00m, Date = baseDate.AddDays(8).AddHours(14),  StoreId = 3, AccountNumber = "ACC456", DomainId = 3, Address = "tb1qj98t1wpez73cnmqviecrnyiwrnqrhwnly", Created = DateTime.Now.AddDays(-52), State = "Completed", ExternalId = "EXT333333333" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP005", Amount = 80.20m,  Rate = 65100.90m, Date = baseDate.AddDays(20).AddHours(15), StoreId = 3, AccountNumber = "ACC456", DomainId = 3, Address = "tb1q0987654321098765432109876543210987654321", Created = DateTime.Now.AddDays(-40), State = "Completed", ExternalId = "EXT222222222" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP016", Amount = 500.00m, Rate = 69800.00m, Date = DateTime.Now.AddDays(-4).AddHours(11), StoreId = 2, AccountNumber = "ACC456", DomainId = 2, Address = "tb1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh", Created = DateTime.Now.AddDays(-4), State = "Completed", ExternalId = "EXT111111111" },

                // Group 3: AccountNumber "ACC789", more recent deposits for newest jar testing
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP006", Amount = 320.00m, Rate = 69100.10m, Date = DateTime.Now.AddDays(-7).AddHours(10), StoreId = 1, AccountNumber = "ACC789", DomainId = 1, Address = "tb1qltcaddressexample1234567890abcdef", Created = DateTime.Now.AddDays(-7), State = "Completed", ExternalId = "EXTLTC001" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP007", Amount = 475.99m, Rate = 69750.00m, Date = DateTime.Now.AddDays(-5).AddHours(12), StoreId = 2, AccountNumber = "ACC789", DomainId = 2, Address = "tb1qdogeaddressexample1234567890abcdef", Created = DateTime.Now.AddDays(-5), State = "Completed", ExternalId = "EXTDOGE001" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP008", Amount = 99.00m,  Rate = 70200.25m, Date = DateTime.Now.AddDays(-3).AddHours(13), StoreId = 1, AccountNumber = "ACC789", DomainId = 1, Address = "tb1qxrpaddressexample1234567890abcdef", Created = DateTime.Now.AddDays(-3), State = "Completed", ExternalId = "EXTXRP001" },

                // Miscellaneous Deposits
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP009", Amount = 15.00m,  Rate = 66000.00m, Date = baseDate.AddDays(25).AddHours(8),  StoreId = 1, AccountNumber = "ACCMISC1", DomainId = 1, Address = "tb1qar0srrr7xfkvy5l643lydnw9re59gtzzwf5mdq", Created = DateTime.Now.AddDays(-35), State = "Completed", ExternalId = "EXTMISC1" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP010", Amount = 88.88m,  Rate = 67300.00m, Date = baseDate.AddDays(30).AddHours(16), StoreId = 2, AccountNumber = "ACCMISC2", DomainId = 2, Address = "tb1q1111111111111111111111111111111111111", Created = DateTime.Now.AddDays(-30), State = "Completed", ExternalId = "EXTMISC2" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP011", Amount = 1800.00m,Rate = 71100.00m, Date = DateTime.Now.AddDays(-1).AddHours(14), StoreId = 1, AccountNumber = "ACCBIG1", DomainId = 1, Address = "tb1q0000000000000000000000000000000000000", Created = DateTime.Now.AddDays(-1), State = "Completed", ExternalId = "EXTBIG1" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP012", Amount = 5.50m,   Rate = 68050.50m, Date = baseDate.AddDays(40).AddHours(17), StoreId = 3, AccountNumber = "ACCSMALL1", DomainId = 3, Address = "tb1qffffffffffffffffffffffffffffffffffffffff", Created = DateTime.Now.AddDays(-20), State = "Completed", ExternalId = "EXTSMALL1" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP017", Amount = 777.00m, Rate = 69400.00m, Date = DateTime.Now.AddDays(-6).AddHours(9),  StoreId = 1, AccountNumber = "ACC789", DomainId = 1, Address = "tb1qmoneroaddressexample1234567890abcdef", Created = DateTime.Now.AddDays(-6), State = "Completed", ExternalId = "EXTXMR001" },
                
                // This deposit (ID 100015) will be used for re-assignment tests
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP_REASSIGN", Amount = 100.00m, Rate = 68800.00m, Date = DateTime.Now.AddDays(-10), StoreId = 1, AccountNumber = "ACC_REASSIGN", DomainId = 1, Address = "tb1qreassign01234567890abcdef01234567890", Created = DateTime.Now.AddDays(-10), State = "Completed", ExternalId = "EXTREASSIGN" },
                new Deposit { Id = initialID++, DocumentNumber = "AD_DEP_SAMEDAY", Amount = 50.00m, Rate = 69760.00m, Date = DateTime.Now.AddDays(-5), StoreId = 2, AccountNumber = "ACC_SAMEDAY", DomainId = 2, Address = "tb1qsameday01234567890abcdef01234567890ab", Created = DateTime.Now.AddDays(-5), State = "Completed", ExternalId = "EXTSAMEDAY" },

                // Group 4: For Tanker Monthly Breakdown Test
                new Deposit { Id = initialID++, DocumentNumber = "DEP_MAY_01", Amount = 0.3m, Rate = 67000.00m, Date = new DateTime(DateTime.Now.Year, 5, 20), StoreId = 1, AccountNumber = "ACC_TNK_ALPHA", DomainId = 1, Address = "tb1qaddressmay01234567890abcdef01234567890", Created = DateTime.Now, State = "Completed", ExternalId = "EXT_MAY_01" },
                new Deposit { Id = initialID++, DocumentNumber = "DEP_JUNE_01", Amount = 0.4m, Rate = 61000.00m, Date = new DateTime(DateTime.Now.Year, 6, 25), StoreId = 1, AccountNumber = "ACC_TNK_BETA", DomainId = 1, Address = "tb1qaddressjune01234567890abcdef01234567890", Created = DateTime.Now, State = "Completed", ExternalId = "EXT_JUNE_01" },
                new Deposit { Id = initialID++, DocumentNumber = "DEP_JULY_01", Amount = 0.7m, Rate = 64000.00m, Date = new DateTime(DateTime.Now.Year, 7, 25), StoreId = 1, AccountNumber = "ACC_TNK_GAMMA", DomainId = 1, Address = "tb1qaddressjuly01234567890abcdef01234567890", Created = DateTime.Now, State = "Completed", ExternalId = "EXT_JULY_01" },
                new Deposit { Id = initialID++, DocumentNumber = "inv-007", Amount = 0.5m, Rate = 65500.00m, Date = new DateTime(DateTime.Now.Year, 8, 1), StoreId = 1, AccountNumber = "ACC_TNK_ROOT", DomainId = 1, Address = "tb1qaddressaug01234567890abcdef01234567890", Created = DateTime.Now, State = "Completed", ExternalId = "EXT_AUG_01" },
                new Deposit { Id = initialID++, DocumentNumber = "inv-008", Amount = 0.6m, Rate = 66200.00m, Date = new DateTime(DateTime.Now.Year, 8, 15), StoreId = 1, AccountNumber = "ACC_TNK_ROOT", DomainId = 1, Address = "tb1qaddressaug02234567890abcdef01234567890", Created = DateTime.Now, State = "Completed", ExternalId = "EXT_AUG_02" }
            };
        }

        private static void SeedJarsAndDetails(IOlapRepository olapStorage, ISearchStorage searchStorage, string currencyCode, List<long> allDepositIds)
        {
            DateTime baseLinkTime = DateTime.Now;

            // Jar V1
            long jarV1 = 1;
            if (olapStorage != null) olapStorage.CreateJar(currencyCode, jarV1, "Historical Batch Jar - V1", baseLinkTime.AddDays(-50));
            if (searchStorage != null) searchStorage.CreateJar(currencyCode, jarV1, "Historical Batch Jar - V1", baseLinkTime.AddDays(-50), null);

            // Jar V2
            long jarV2 = 2;
            if (olapStorage != null) olapStorage.CreateJar(currencyCode, jarV2, "Regular Processing Jar - V2", baseLinkTime.AddDays(-25), jarV1);
            if (searchStorage != null) searchStorage.CreateJar(currencyCode, jarV2, "Regular Processing Jar - V2", baseLinkTime.AddDays(-25), jarV1);

            // Jar V3
            long jarV3 = 3;
            if (olapStorage != null) olapStorage.CreateJar(currencyCode, jarV3, "Current Active Jar - V3", baseLinkTime.AddDays(-2), jarV2);
            if (searchStorage != null) searchStorage.CreateJar(currencyCode, jarV3, "Current Active Jar - V3", baseLinkTime.AddDays(-2), jarV2);

            if (olapStorage != null && allDepositIds.Any())
            {
                Debug.WriteLine($"DataSeeder: Seeding jars and linking deposits.");

                Func<int, long?> getDepositIdAtIndex = (index) => allDepositIds.Count > index ? (long?)allDepositIds[index] : null;
                long depositForReassignId = allDepositIds.First(id => id == 100015); // AD_DEP_REASSIGN
                long depositForSameDayId = allDepositIds.First(id => id == 100016); // AD_DEP_SAMEDAY

                TryAddJarDetail(olapStorage, currencyCode, jarV1, getDepositIdAtIndex(0), baseLinkTime.AddDays(-50).AddHours(1)); // AD_DEP001
                TryAddJarDetail(olapStorage, currencyCode, jarV1, getDepositIdAtIndex(4), baseLinkTime.AddDays(-50).AddHours(2)); // AD_DEP004

                TryAddJarDetail(olapStorage, currencyCode, jarV2, getDepositIdAtIndex(1), baseLinkTime.AddDays(-25).AddHours(1)); // AD_DEP002
                TryAddJarDetail(olapStorage, currencyCode, jarV2, getDepositIdAtIndex(5), baseLinkTime.AddDays(-25).AddHours(2)); // AD_DEP005
                TryAddJarDetail(olapStorage, currencyCode, jarV2, getDepositIdAtIndex(10), baseLinkTime.AddDays(-25).AddHours(3)); // AD_DEP009

                TryAddJarDetail(olapStorage, currencyCode, jarV3, getDepositIdAtIndex(7), baseLinkTime.AddDays(-2).AddHours(1));  // AD_DEP006
                TryAddJarDetail(olapStorage, currencyCode, jarV3, getDepositIdAtIndex(8), baseLinkTime.AddDays(-2).AddHours(2));  // AD_DEP007
                TryAddJarDetail(olapStorage, currencyCode, jarV3, getDepositIdAtIndex(9), baseLinkTime.AddDays(-2).AddHours(3));  // AD_DEP008
                TryAddJarDetail(olapStorage, currencyCode, jarV3, getDepositIdAtIndex(12), baseLinkTime.AddDays(-2).AddHours(4)); // AD_DEP011
                TryAddJarDetail(olapStorage, currencyCode, jarV3, getDepositIdAtIndex(3), baseLinkTime.AddDays(-2).AddHours(5));  // AD_DEP015
                TryAddJarDetail(olapStorage, currencyCode, jarV3, getDepositIdAtIndex(6), baseLinkTime.AddDays(-2).AddHours(6));  // AD_DEP016
                TryAddJarDetail(olapStorage, currencyCode, jarV3, getDepositIdAtIndex(14), baseLinkTime.AddDays(-2).AddHours(7)); // AD_DEP017

                // Re-assignment Tests for Jars (Deposit 100015: AD_DEP_REASSIGN)
                Debug.WriteLine($"DataSeeder: Jar re-assignment test for Deposit ID {depositForReassignId}.");
                // 1. Assign to JarV1
                TryAddJarDetail(olapStorage, currencyCode, jarV1, depositForReassignId, baseLinkTime.AddDays(-9));
                // 2. Move to JarV2
                TryAddJarDetail(olapStorage, currencyCode, jarV2, depositForReassignId, baseLinkTime.AddDays(-8));
                // 3. Move back to JarV1 (this should be the final assignment for totals)
                TryAddJarDetail(olapStorage, currencyCode, jarV1, depositForReassignId, baseLinkTime.AddDays(-7));

                // Same-day re-assignment test for Jars (Deposit 100016: AD_DEP_SAMEDAY)
                Debug.WriteLine($"DataSeeder: Jar same-day re-assignment test for Deposit ID {depositForSameDayId}.");
                // 1. Assign to JarV2
                TryAddJarDetail(olapStorage, currencyCode, jarV2, depositForSameDayId, baseLinkTime.AddDays(-5).AddHours(10)); // 10:00 AM
                                                                                                                               // 2. Move to JarV3 (latest on this day)
                TryAddJarDetail(olapStorage, currencyCode, jarV3, depositForSameDayId, baseLinkTime.AddDays(-5).AddHours(14)); // 02:00 PM
                                                                                                                               // 3. Erroneously try to assign to JarV1 earlier on same day (should be ignored for final state due to later JarV3 assignment)
                TryAddJarDetail(olapStorage, currencyCode, jarV1, depositForSameDayId, baseLinkTime.AddDays(-5).AddHours(8));  // 08:00 AM (MV should pick JarV3 due to timestamp)


                Debug.WriteLine("DataSeeder: Finished seeding Jars and JarDetails.");
            }
        }

        private static void TryAddJarDetail(IOlapRepository olapStorage, string currencyCode, long jarVersion, long? depositId, DateTime created)
        {
            if (depositId.HasValue)
            {
                olapStorage.CreateJarDetailIfNotExists(currencyCode, jarVersion, depositId.Value, created);
                Debug.WriteLine($"DataSeeder: Linked Deposit ID {depositId.Value} to Jar Version {jarVersion} at {created:yyyy-MM-dd HH:mm:ss}");
            }
        }

        private static void SeedTanksAndDetails(IOlapRepository olapStorage, ISearchStorage searchStorage, string currencyCode, List<Deposit> allDeposits)
        {
            if (olapStorage == null) return;
            var allDepositIds = allDeposits.Select(d => d.Id).ToList();
            DateTime baseLinkTime = DateTime.Now;
            
            int initialID = 1000000;
            // --- Create multiple versions for Tank A (ID 1000000) for version comparison testing ---
            long tankA_Id = initialID++;
            Debug.WriteLine($"DataSeeder: Seeding versions for Tank ID {tankA_Id}.");
            var tankA_V1 = new Tank { Id = tankA_Id, Version = 1, Description = "Primary Holding Tank A - V1", Created = baseLinkTime.AddDays(-40), OriginId = 1, OriginType = "Jar" };
            olapStorage.CreateTank(currencyCode, tankA_V1);
            if (searchStorage != null) searchStorage.CreateTank(currencyCode, tankA_V1);

            var tankA_V2 = new Tank { Id = tankA_Id, Version = 2, Description = "Primary Holding Tank A - V2 (updated)", Created = baseLinkTime.AddDays(-35), OriginId = 1, OriginType = "Jar" };
            olapStorage.CreateTank(currencyCode, tankA_V2);
            if (searchStorage != null) searchStorage.CreateTank(currencyCode, tankA_V2);

            var tankA_V3 = new Tank { Id = tankA_Id, Version = 3, Description = "Primary Holding Tank A - V3 (final)", Created = baseLinkTime.AddDays(-30), OriginId = 1, OriginType = "Jar" };
            olapStorage.CreateTank(currencyCode, tankA_V3);
            if (searchStorage != null) searchStorage.CreateTank(currencyCode, tankA_V3);

            var tankB = new Tank { Id = initialID++, Version = 1, Description = "Secondary Processing Tank B", Created = baseLinkTime.AddDays(-10), OriginId = 2, OriginType = "Jar" };
            olapStorage.CreateTank(currencyCode, tankB);
            if (searchStorage != null) searchStorage.CreateTank(currencyCode, tankB);

            var tankC = new Tank { Id = initialID++, Version = 1, Description = "Reserve Tank C", Created = baseLinkTime.AddDays(-5), OriginId = 1000000, OriginType = "Tank" };
            olapStorage.CreateTank(currencyCode, tankC);
            if (searchStorage != null) searchStorage.CreateTank(currencyCode, tankC);

            var alphaTankMay = new Tank { Id = initialID++, Version = 1, Description = "Alpha-Tank-May", Created = new DateTime(DateTime.Now.Year, 5, 20), OriginId = 1, OriginType = "Jar" };
            olapStorage.CreateTank(currencyCode, alphaTankMay);
            var betaTankJune = new Tank { Id = initialID++, Version = 1, Description = "Beta-Tank-June", Created = new DateTime(DateTime.Now.Year, 6, 25), OriginId = 1, OriginType = "Jar" };
            olapStorage.CreateTank(currencyCode, betaTankJune);
            var gammaTankJuly = new Tank { Id = initialID++, Version = 1, Description = "Gamma-Tank-July", Created = new DateTime(DateTime.Now.Year, 7, 25), OriginId = 1, OriginType = "Jar" };
            olapStorage.CreateTank(currencyCode, gammaTankJuly);

            if (allDeposits.Any())
            {
                Debug.WriteLine($"DataSeeder: Seeding tanks and linking deposits.");

                Func<int, long?> getDepositIdAtIndex = (index) => allDepositIds.Count > index ? (long?)allDepositIds[index] : null;
                long depositForReassignId = allDeposits.First(d => d.DocumentNumber == "AD_DEP_REASSIGN").Id;
                long depositForSameDayId = allDeposits.First(d => d.DocumentNumber == "AD_DEP_SAMEDAY").Id;

                // Deposit for V1. When querying V1, only this deposit should appear.
                TryAddTankDetail(olapStorage, currencyCode, tankA_Id, tankA_V1.Version, getDepositIdAtIndex(0), baseLinkTime.AddDays(-41));
                // Deposit for V2. When querying V2, deposits for V1 and V2 should appear.
                TryAddTankDetail(olapStorage, currencyCode, tankA_Id, tankA_V2.Version, getDepositIdAtIndex(1), baseLinkTime.AddDays(-36));
                // Deposit for V3. When querying V3, deposits for V1, V2 and V3 should appear.
                TryAddTankDetail(olapStorage, currencyCode, tankA_Id, tankA_V3.Version, getDepositIdAtIndex(7), baseLinkTime.AddDays(-31));

                TryAddTankDetail(olapStorage, currencyCode, tankB.Id, tankB.Version, getDepositIdAtIndex(2), baseLinkTime.AddDays(-10).AddHours(1));
                TryAddTankDetail(olapStorage, currencyCode, tankB.Id, tankB.Version, getDepositIdAtIndex(3), baseLinkTime.AddDays(-10).AddHours(2));
                TryAddTankDetail(olapStorage, currencyCode, tankB.Id, tankB.Version, getDepositIdAtIndex(8), baseLinkTime.AddDays(-10).AddHours(3));
                TryAddTankDetail(olapStorage, currencyCode, tankB.Id, tankB.Version, getDepositIdAtIndex(12), baseLinkTime.AddDays(-10).AddHours(4));

                // Tank Re-assignment Tests (Deposit 100015: AD_DEP_REASSIGN)
                Debug.WriteLine($"DataSeeder: Tank re-assignment test for Deposit ID {depositForReassignId}.");
                // 1. Assign to Tank A (as part of its V2 history). It will appear in queries for V2 and V3 of Tank A.
                TryAddTankDetail(olapStorage, currencyCode, tankA_Id, tankA_V2.Version, depositForReassignId, baseLinkTime.AddDays(-36).AddHours(1));
                // 2. Move to Tank2Id
                TryAddTankDetail(olapStorage, currencyCode, tankB.Id, tankB.Version, depositForReassignId, baseLinkTime.AddDays(-6).AddHours(12)); // Same day, later time
                                                                                                                                    // 3. Move to Tank3Id (this should be the final assignment)
                TryAddTankDetail(olapStorage, currencyCode, tankC.Id, tankC.Version, depositForReassignId, baseLinkTime.AddDays(-5).AddHours(10)); // Next day

                // Same-day re-assignment test for Tanks (Deposit 100016: AD_DEP_SAMEDAY)
                Debug.WriteLine($"DataSeeder: Tank same-day re-assignment test for Deposit ID {depositForSameDayId}.");
                // 1. Assign to Tank1Id
                TryAddTankDetail(olapStorage, currencyCode, tankA_Id, tankA_V3.Version, depositForSameDayId, baseLinkTime.AddDays(-4).AddHours(8));  // 08:00 AM
                                                                                                                                   // 2. Move to Tank2Id (latest on this day)
                TryAddTankDetail(olapStorage, currencyCode, tankB.Id, tankB.Version, depositForSameDayId, baseLinkTime.AddDays(-4).AddHours(15)); // 03:00 PM
                                                                                                                                   // 3. Erroneously assign to Tank3Id earlier on same day (MV should pick Tank2Id due to timestamp)
                TryAddTankDetail(olapStorage, currencyCode, tankC.Id, tankC.Version, depositForSameDayId, baseLinkTime.AddDays(-4).AddHours(10)); // 10:00 AM 

                Debug.WriteLine($"DataSeeder: Linking deposits to new monthly tanks.");
                long depositMayId = allDeposits.First(d => d.DocumentNumber == "DEP_MAY_01").Id;
                long depositJuneId = allDeposits.First(d => d.DocumentNumber == "DEP_JUNE_01").Id;
                long depositJulyId = allDeposits.First(d => d.DocumentNumber == "DEP_JULY_01").Id;
                TryAddTankDetail(olapStorage, currencyCode, alphaTankMay.Id, alphaTankMay.Version, depositMayId, alphaTankMay.Created.AddHours(1));
                TryAddTankDetail(olapStorage, currencyCode, betaTankJune.Id, betaTankJune.Version, depositJuneId, betaTankJune.Created.AddHours(1));
                TryAddTankDetail(olapStorage, currencyCode, gammaTankJuly.Id, gammaTankJuly.Version, depositJulyId, gammaTankJuly.Created.AddHours(1));

                Debug.WriteLine("DataSeeder: Finished seeding Tanks and TankDetails.");
            }
        }

        private static void TryAddTankDetail(IOlapRepository olapStorage, string currencyCode, long tankId, int tankVersion, long? depositId, DateTime created)
        {
            if (depositId.HasValue)
            {
                olapStorage.CreateTankDetailIfNotExists(currencyCode, tankId, tankVersion, depositId.Value, created);
                Debug.WriteLine($"DataSeeder: Linked Deposit ID {depositId.Value} to Tank ID {tankId} at {created:yyyy-MM-dd HH:mm:ss}");
            }
        }

        private static void SeedTankersAndDetails(IOlapRepository olapStorage, ISearchStorage searchStorage, string currencyCode, List<Deposit> allDeposits)
        {
            if (olapStorage == null) return;
            var allDepositIds = allDeposits.Select(d => d.Id).ToList();
            DateTime baseLinkTime = DateTime.Now;

            int initialID = 2000000;
            long tankerX_Id = initialID++;
            var tankerX_V1 = new Tanker { Id = tankerX_Id, Version = 1, Description = "Main Distribution Tanker X - V1", Created = baseLinkTime.AddDays(-30) };
            olapStorage.CreateTanker(currencyCode, tankerX_V1);
            if (searchStorage != null) searchStorage.CreateTanker(currencyCode, tankerX_V1);

            var tankerX_V2 = new Tanker { Id = tankerX_Id, Version = 2, Description = "Main Distribution Tanker X - V2 (expanded)", Created = baseLinkTime.AddDays(-25) };
            olapStorage.CreateTanker(currencyCode, tankerX_V2);
            if (searchStorage != null) searchStorage.CreateTanker(currencyCode, tankerX_V2);

            var tankerX_V3 = new Tanker { Id = tankerX_Id, Version = 3, Description = "Main Distribution Tanker X - V3 (final)", Created = baseLinkTime.AddDays(-20) };
            olapStorage.CreateTanker(currencyCode, tankerX_V3);
            if (searchStorage != null) searchStorage.CreateTanker(currencyCode, tankerX_V3);

            var tankerY = new Tanker { Id = initialID++, Version = 1, Description = "Regional Supply Tanker Y2", Created = baseLinkTime.AddDays(-8) };
            olapStorage.CreateTanker(currencyCode, tankerY);
            if (searchStorage != null) searchStorage.CreateTanker(currencyCode, tankerY);

            var tankerZ = new Tanker { Id = initialID++, Version = 1, Description = "Standby Tanker Z3", Created = baseLinkTime.AddDays(-3) };
            olapStorage.CreateTanker(currencyCode, tankerZ);
            if (searchStorage != null) searchStorage.CreateTanker(currencyCode, tankerZ);

            Debug.WriteLine($"DataSeeder: Seeding new tanker for monthly breakdown test.");
            var mainTestTanker = new Tanker { Id = initialID++, Description = "A tanker for Alpha, Beta, and Gamma tanks", Created = new DateTime(DateTime.Now.Year, 8, 20) };
            olapStorage.CreateTanker(currencyCode, mainTestTanker);

            if (allDeposits.Any())
            {
                Debug.WriteLine($"DataSeeder: Seeding tankers and linking deposits.");

                Func<int, long?> getDepositIdAtIndex = (index) => allDepositIds.Count > index ? (long?)allDepositIds[index] : null;
                long depositForReassignId = allDeposits.First(d => d.DocumentNumber == "AD_DEP_REASSIGN").Id;

                TryAddTankerDetail(olapStorage, currencyCode, tankerX_Id, tankerX_V1.Version, getDepositIdAtIndex(4), baseLinkTime.AddDays(-30).AddHours(1));
                TryAddTankerDetail(olapStorage, currencyCode, tankerX_Id, tankerX_V2.Version, getDepositIdAtIndex(5), baseLinkTime.AddDays(-25).AddHours(2));
                TryAddTankerDetail(olapStorage, currencyCode, tankerX_Id, tankerX_V3.Version, getDepositIdAtIndex(9), baseLinkTime.AddDays(-20).AddHours(3));

                TryAddTankerDetail(olapStorage, currencyCode, tankerY.Id, tankerY.Version, getDepositIdAtIndex(6), baseLinkTime.AddDays(-8).AddHours(1));
                TryAddTankerDetail(olapStorage, currencyCode, tankerY.Id, tankerY.Version, getDepositIdAtIndex(10), baseLinkTime.AddDays(-8).AddHours(2));
                TryAddTankerDetail(olapStorage, currencyCode, tankerY.Id, tankerY.Version, getDepositIdAtIndex(11), baseLinkTime.AddDays(-8).AddHours(3));
                TryAddTankerDetail(olapStorage, currencyCode, tankerY.Id, tankerY.Version, getDepositIdAtIndex(14), baseLinkTime.AddDays(-8).AddHours(4));

                // Tanker Re-assignment (Deposit 100015: AD_DEP_REASSIGN)
                Debug.WriteLine($"DataSeeder: Tanker re-assignment test for Deposit ID {depositForReassignId}.");
                // 1. Assign to Tanker1
                TryAddTankerDetail(olapStorage, currencyCode, tankerX_Id, tankerX_V3.Version, depositForReassignId, baseLinkTime.AddDays(-4).AddHours(1));
                // 2. Move to Tanker2
                TryAddTankerDetail(olapStorage, currencyCode, tankerY.Id, tankerY.Version, depositForReassignId, baseLinkTime.AddDays(-4).AddHours(5)); // Same day, later
                                                                                                                                       // 3. Move to Tanker3 (final)
                TryAddTankerDetail(olapStorage, currencyCode, tankerZ.Id, tankerZ.Version, depositForReassignId, baseLinkTime.AddDays(-3).AddHours(1)); // Next day

                Debug.WriteLine($"DataSeeder: Linking deposits to new monthly test tanker.");
                long depositMayId = allDeposits.First(d => d.DocumentNumber == "DEP_MAY_01").Id;
                long depositJuneId = allDeposits.First(d => d.DocumentNumber == "DEP_JUNE_01").Id;
                long depositJulyId = allDeposits.First(d => d.DocumentNumber == "DEP_JULY_01").Id;
                long rootDeposit1Id = allDeposits.First(d => d.DocumentNumber == "inv-007").Id;
                long rootDeposit2Id = allDeposits.First(d => d.DocumentNumber == "inv-008").Id;

                // All these deposits end up in our main test tanker (ID 1)
                var tankerLinkTime = mainTestTanker.Created.AddHours(1);
                TryAddTankerDetail(olapStorage, currencyCode, mainTestTanker.Id, mainTestTanker.Version, depositMayId, tankerLinkTime);
                TryAddTankerDetail(olapStorage, currencyCode, mainTestTanker.Id, mainTestTanker.Version, depositJuneId, tankerLinkTime);
                TryAddTankerDetail(olapStorage, currencyCode, mainTestTanker.Id, mainTestTanker.Version, depositJulyId, tankerLinkTime);
                TryAddTankerDetail(olapStorage, currencyCode, mainTestTanker.Id, mainTestTanker.Version, rootDeposit1Id, tankerLinkTime);
                TryAddTankerDetail(olapStorage, currencyCode, mainTestTanker.Id, mainTestTanker.Version, rootDeposit2Id, tankerLinkTime);

                Debug.WriteLine("DataSeeder: Finished seeding Tankers and TankerDetails.");
            }
        }

        private static void TryAddTankerDetail(IOlapRepository olapStorage, string currencyCode, long tankerId, int tankerVersion, long? depositId, DateTime created)
        {
            if (depositId.HasValue)
            {
                olapStorage.CreateTankerDetailIfNotExists(currencyCode, tankerId, depositId.Value, tankerVersion, created);
                Debug.WriteLine($"DataSeeder: Linked Deposit ID {depositId.Value} to Tanker ID {tankerId} at {created:yyyy-MM-dd HH:mm:ss}");
            }
        }

        private static void SeedWithdrawalsAndDetails(IOlapRepository olapStorage, ISearchStorage searchStorage, string currencyCode)
        {
            Debug.WriteLine("DataSeeder: Seeding example Withdrawals, Bottles, Dispensers, and their details.");

            long initialID = 200000;
            var withdrawals = new List<Withdrawal>
            {
                new Withdrawal { Id = initialID++, DocumentNumber = "WD001", Amount = 100.00m, Rate = 70100.00m, Date = DateTime.Now.AddDays(-3), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "tb1qwithdrawalcryptoaddr1111111111111111111", Created = DateTime.Now.AddDays(-3), State = "Awaiting", ExternalId = "EXTWD001" },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD002", Amount = 50.50m,  Rate = 70550.50m, Date = DateTime.Now.AddDays(-2), StoreId = 2, AccountNumber = "ACC456", DomainId = 2, Address = "tb1qwithdrawalcryptoaddr2222222222222222", Created = DateTime.Now.AddDays(-2), State = "Awaiting", ExternalId = "EXTWD002" },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD_REASSIGN", Amount = 75.00m, Rate = 69500.00m, Date = DateTime.Now.AddDays(-5), StoreId = 1, AccountNumber = "ACC_WD_REASSIGN", DomainId = 1, Address = "tb1qwithdrawreassignaddr000000000000000000", Created = DateTime.Now.AddDays(-5), State = "Awaiting", ExternalId = "EXTWDREASSIGN" },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD003", Amount = 20.00m, Rate = 70600.00m, Date = DateTime.Now.AddDays(-2), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "tb1qwithdrawalcryptoaddr_acc123_2", Created = DateTime.Now.AddDays(-2), State = "Awaiting", ExternalId = "EXTWD003" },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD004", Amount = 30.00m, Rate = 71200.00m, Date = DateTime.Now.AddDays(-1), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "tb1qwithdrawalcryptoaddr_acc123_3", Created = DateTime.Now.AddDays(-1), State = "Awaiting", ExternalId = "EXTWD004" },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD005", Amount = 40.00m, Rate = 71500.00m, Date = DateTime.Now.AddHours(-10), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "tb1qwithdrawalcryptoaddr_acc123_4", Created = DateTime.Now.AddHours(-10), State = "Awaiting", ExternalId = "EXTWD005" },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD006", Amount = 50.00m, Rate = 71800.00m, Date = DateTime.Now.AddHours(-5), StoreId = 1, AccountNumber = "ACC123", DomainId = 1, Address = "tb1qwithdrawalcryptoaddr1111111111111111111", Created = DateTime.Now.AddHours(-5), State = "Awaiting", ExternalId = "EXTWD006" },
                new Withdrawal { Id = initialID++, DocumentNumber = "WD007", Amount = 60.00m, Rate = 69800.00m, Date = DateTime.Now.AddDays(-4), StoreId = 2, AccountNumber = "ACC456", DomainId = 2, Address = "tb1qwithdrawalcryptoaddr_acc456_2", Created = DateTime.Now.AddDays(-4), State = "Awaiting", ExternalId = "EXTWD007" }
            };

            var withdrawalIds = new List<long>();
            foreach (var wd in withdrawals)
            {
                if (olapStorage != null) olapStorage.CreateWithdrawal(currencyCode, wd);
                if (searchStorage != null) searchStorage.CreateWithdrawal(currencyCode, wd);
                withdrawalIds.Add(wd.Id);
            }

            var bottles = new List<Bottle>
            {
                new Bottle { Id = 300000, Description = "Bottle A", Created = DateTime.Now.AddDays(-3) },
                new Bottle { Id = 300001, Description = "Bottle B", Created = DateTime.Now.AddDays(-2) }
            };
            var bottleIds = new List<long>();
            foreach (var b in bottles)
            {
                if (olapStorage != null) olapStorage.CreateBottle(currencyCode, b);
                if (searchStorage != null) searchStorage.CreateBottle(currencyCode, b);
                bottleIds.Add(b.Id);
            }

            var dispensers = new List<Dispenser>
            {
                new Dispenser { Id = 400000, Description = "Dispenser X", Created = DateTime.Now.AddDays(-3), Version = 1 },
                new Dispenser { Id = 400001, Description = "Dispenser Y", Created = DateTime.Now.AddDays(-2), Version = 1 },
                new Dispenser { Id = 400002, Description = "Dispenser Z (for reassign)", Created = DateTime.Now.AddDays(-1), Version = 1 }
            };
            var dispenserIds = new List<long>();
            foreach (var d in dispensers)
            {
                if (olapStorage != null) olapStorage.CreateDispenser(currencyCode, d);
                if (searchStorage != null) searchStorage.CreateDispenser(currencyCode, d);
                dispenserIds.Add(d.Id);
            }

            if (olapStorage != null)
            {
                Debug.WriteLine($"DataSeeder: Seeding withdrawal details for OLAP.");
                long withdrawalForReassignId = withdrawals.First(w => w.DocumentNumber == "WD_REASSIGN").Id;
                long dispenserXId = dispensers.First(d => d.Description == "Dispenser X").Id;
                long dispenserYId = dispensers.First(d => d.Description == "Dispenser Y").Id;
                long dispenserZId = dispensers.First(d => d.Description == "Dispenser Z (for reassign)").Id;

                if (withdrawalIds.Count > 0 && bottleIds.Count > 0)
                {
                    olapStorage.CreateBottleDetailIfNotExists(currencyCode, withdrawalIds[0], bottleIds[0], DateTime.Now.AddDays(-3));
                    olapStorage.CreateBottleDetailIfNotExists(currencyCode, withdrawalIds[1], bottleIds[1], DateTime.Now.AddDays(-2));
                }

                // Dispenser re-assignment for withdrawalForReassignId
                if (withdrawalForReassignId != 0)
                {
                    Debug.WriteLine($"DataSeeder: Dispenser re-assignment test for Withdrawal ID {withdrawalForReassignId}.");
                    // 1. Assign to Dispenser X
                    olapStorage.CreateDispenserDetailIfNotExists(currencyCode, withdrawalForReassignId, dispenserXId, DateTime.Now.AddDays(-5).AddHours(10), 1);
                    // 2. Move to Dispenser Y on same day but later
                    olapStorage.CreateDispenserDetailIfNotExists(currencyCode, withdrawalForReassignId, dispenserYId, DateTime.Now.AddDays(-5).AddHours(12), 1);
                    // 3. Move to Dispenser Z on a later day (final assignment)
                    olapStorage.CreateDispenserDetailIfNotExists(currencyCode, withdrawalForReassignId, dispenserZId, DateTime.Now.AddDays(-4).AddHours(9), 1);

                    // Regular assignments for other withdrawals
                    if (withdrawalIds.Count > 1)
                        olapStorage.CreateDispenserDetailIfNotExists(currencyCode, withdrawalIds[0], dispenserXId, DateTime.Now.AddDays(-3).AddHours(11), 1); // WD001 to Dispenser X
                    if (withdrawalIds.Count > 2) // Assuming WD002 is withdrawalIds[1]
                        olapStorage.CreateDispenserDetailIfNotExists(currencyCode, withdrawalIds[1], dispenserYId, DateTime.Now.AddDays(-2).AddHours(13), 1); // WD002 to Dispenser Y
                }

                Debug.WriteLine("DataSeeder: Finished seeding Withdrawals, Bottles, Dispensers, and their details.");
            }
        }

        private static void SeedInvoicePayments(ISearchStorage searchStorage, string currencyCode)
        {
            if (searchStorage == null) return;

            Debug.WriteLine("DataSeeder: Seeding example Invoice Payments for search index.");

            var payments = new List<InvoicePayment>
            {
                // Payments for externalAtAddress "ext-addr-alpha"
                new InvoicePayment
                {
                    Kind = currencyCode, InvoiceId = "INV-2024-ALPHA-001",
                    DestinationAddress = "tb1qn73twe8nkjwss93qdhuadkp0hylz0dnpgh897p",
                    ExternalAtAddress = "ext-addr-alpha", PaidAmount = 0.00000075m, PaidAt = DateTime.Now.AddDays(-10)
                },
                new InvoicePayment
                {
                    Kind = currencyCode, InvoiceId = "INV-2024-ALPHA-002",
                    DestinationAddress = "tb1qabcdef0123456789abcdef0123456789abcdef01",
                    ExternalAtAddress = "ext-addr-alpha", PaidAmount = 25.00m, PaidAt = DateTime.Now.AddDays(-15) // Oldest for this address
                },
                new InvoicePayment
                {
                    Kind = currencyCode, InvoiceId = "INV-2024-ALPHA-003",
                    DestinationAddress = "tb1qn73twe8nkjwss93qdhuadkp0hylz0dnpgh897p",
                    ExternalAtAddress = "ext-addr-alpha", PaidAmount = 0.00000075m, PaidAt = DateTime.Now.AddDays(-5) // Most recent for this address
                },
                // Payments for externalAtAddress "ext-addr-beta"
                new InvoicePayment
                {
                    Kind = currencyCode, InvoiceId = "INV-2024-BETA-001",
                    DestinationAddress = "tb1q1234567890123456789012345678901234567890",
                    ExternalAtAddress = "ext-addr-beta", PaidAmount = 300.00m, PaidAt = DateTime.Now.AddDays(-8)
                },
            };

            foreach (var payment in payments)
            {
                searchStorage.CreateInvoicePayment(currencyCode, payment);
            }

            Debug.WriteLine("DataSeeder: Finished seeding Invoice Payments.");
        }
    }
}