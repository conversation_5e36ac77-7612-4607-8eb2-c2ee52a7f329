﻿using GamesEngine.Business.Liquidity;
using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineTests.Unit_Tests.Business
{
    [TestClass]
    public class TankTest
    {
        [TestMethod]
        public void TestTankCreation()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            Assert.IsTrue(liquid.Source.Jar.PreviousLegacyJar == null);

            var deposit1 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", liquid.Source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 0);

            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 1);

            var deposit2 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address2", liquid.Source.NextDepositId(), $"1{txid}", 12, 31, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address3", liquid.Source.NextDepositId(), $"1{txid}", 13, 11, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address4", liquid.Source.NextDepositId(), $"1{txid}", 14, 15, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address5", liquid.Source.NextDepositId(), $"1{txid}", 16, 16, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address6", liquid.Source.NextDepositId(), $"1{txid}", 18, 17, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address7", liquid.Source.NextDepositId(), $"1{txid}", 19, 91, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7);

            int tankId = liquid.Source.NextTankId();
            int jarVerion = liquid.Source.NextJarVersion();

            var tank1 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Jar.Amount == 6);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", 1, $"1{txid}", 1, 22, "tb1q4...", 1M, 100000, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7);

            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 0);
            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit2);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 1);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank2 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);

            tankId = liquid.Source.NextTankId();
            var mergedTank = tank1.MergeWith(itIsThePresent, tankId, DateTime.Now, tank2);

            Assert.IsTrue(mergedTank.ExplandedDeposits.Count() == 2);
        }

        [TestMethod]
        public void TestMergedTankCreation()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            liquid.Source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 44, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Amount == 0);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquid.Source.Amount == 1);

            var deposit2 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);     
            var deposit3 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);            
            Assert.IsTrue(liquid.Source.Amount == 1);

            int tankId = liquid.Source.NextTankId();
            int jarVerion = liquid.Source.NextJarVersion();
            var tank1 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Amount == 1);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank2 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank3 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Amount == 3);
            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank4 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);

            //Merged

            tankId = liquid.Source.NextTankId();
            tank1.MergeWith(itIsThePresent, tankId, DateTime.Now, tank2);
            TankReady mergedTank = liquid.Source.FindTank(tankId) as TankReady;

            Assert.IsTrue(mergedTank.ExplandedDeposits.Count() == 2);

            var findMergeTank = liquid.Source.FindTank(tankId);
            Assert.IsTrue(mergedTank == findMergeTank);

            mergedTank.MergeWith(tank3).MergeWith(tank4);
            mergedTank = liquid.Source.FindTank(tankId) as TankReady;
            Assert.IsTrue(mergedTank.Version == 3);
            Assert.IsTrue(mergedTank.ExplandedDeposits.Count() == 4);

            Assert.IsTrue(mergedTank != null);
            Assert.IsTrue(mergedTank.Id == tankId);
            Assert.IsTrue(mergedTank.Name == $"Merged Tank {tankId}");
            //Assert.IsTrue(tankReadyMerged.Version == activeJar.PreviousLegacyJar.Version);
            Assert.IsTrue(mergedTank.ExplandedDeposits.Count() == 4);
            Assert.IsTrue(mergedTank.Amount == 4);
        }


        [TestMethod]
        public void TestDetailOfTank()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            Assert.IsTrue(liquid.Source.Jar.PreviousLegacyJar == null);

            var deposit1 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", 1, $"1{txid}", 1, 77, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 0);

            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 1);

            var deposit2 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address2", 2, $"1{txid}", 1, 5, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address3", 3, $"1{txid}", 1, 6, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address4", 4, $"1{txid}", 1, 5, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address5", 5, $"1{txid}", 1, 3, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address6", 6, $"1{txid}", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address7", 7, $"1{txid}", 1, 6, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7);

            int tankId = liquid.Source.NextTankId();
            int jarVerion = liquid.Source.NextJarVersion();

            var tank1 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Jar.Amount == 6);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address8", 8, $"1{txid}", 22, 77, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7);

            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 0);
            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit2);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 1);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank2 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);

            liquid.Source.ExistTank(tank1.Id);
            
            foreach (var deposit in tank1.ExplandedDeposits)
            {
                Console.WriteLine($"Deposit ID: {deposit.Id}, Amount: {deposit.Amount}, Address: {deposit.Address}, CreatedAt: {deposit.CreatedAt}");
            }
        }

        [TestMethod]
        public void TestMoveDepositToExistTank()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            Assert.IsTrue(liquid.Source.Jar.PreviousLegacyJar == null);

            var deposit1 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", liquid.Source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1M);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 0);

            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1M);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 1M);

            var deposit2 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address2", liquid.Source.NextDepositId(), $"1{txid}", 12, 31, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address3", liquid.Source.NextDepositId(), $"1{txid}", 13, 11, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address4", liquid.Source.NextDepositId(), $"1{txid}", 14, 15, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address5", liquid.Source.NextDepositId(), $"1{txid}", 16, 16, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address6", liquid.Source.NextDepositId(), $"1{txid}", 18, 17, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address7", liquid.Source.NextDepositId(), $"1{txid}", 19, 91, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7M);

            int jarVerion = liquid.Source.NextJarVersion();

            var tank1 = liquid.Source.Jar.CreateTank(itIsThePresent, now, liquid.Source.NextTankId(), "Tank1", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Jar.Amount == 6M);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == 1);
            Assert.IsTrue(tank1.Name == "Tank1");

            liquid.Source.Jar.CreateDraftDeposit(itIsThePresent, now, "address1", 1, $"1{txid}", 1, 22, "tb1q4...", 1M, 100000, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7M);

            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 0);
            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit2);
            Assert.IsTrue(liquid.Source.Jar.AvailableAmount == 1M);

            jarVerion = liquid.Source.NextJarVersion();

            var tank2 = liquid.Source.Jar.CreateTank(itIsThePresent, now, liquid.Source.NextTankId(), "Tank2", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == 2);
            Assert.IsTrue(tank2.Name == "Tank2");

            TankReady mergedTank = tank1.MergeWith(itIsThePresent, liquid.Source.NextTankId(), DateTime.Now, tank2);
            Assert.IsTrue(mergedTank.Version == 1);
            Assert.IsTrue(mergedTank.ExplandedDeposits.Count() == 2);
            jarVerion = liquid.Source.NextJarVersion();
            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit3);
            liquid.Source.Jar.ConfirmDeposit(itIsThePresent, now, deposit4);

            List<Deposit> deposits = new List<Deposit> { deposit3, deposit4 };

            liquid.Source.Jar.MoveToTank(itIsThePresent, now, jarVerion, mergedTank.Id, deposits);

            Assert.IsTrue(liquid.Source.FindTank(mergedTank.Id) is TankReady);
            mergedTank = liquid.Source.FindTank(mergedTank.Id) as TankReady;
            Assert.IsTrue(mergedTank.Version == 2);

            Assert.IsTrue(mergedTank.ExplandedDeposits.Count() == 4);
        }

        [TestMethod]
        public void TestTankChangeColorCreation()
        {
            CurrenciesTest.AddCurrencies();
            string yellow = "#011203";
            string green = "#011204";
            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            Jar activeJar = null;

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            activeJar = source.Jar;

            Assert.IsTrue(activeJar.PreviousLegacyJar == null);

            var deposit1 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address1", source.NextDepositId(), $"1{txid}", 1, 1, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(activeJar.Amount == 1M);
            Assert.IsTrue(activeJar.AvailableAmount == 0);

            activeJar.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(activeJar.Amount == 1M);
            Assert.IsTrue(activeJar.AvailableAmount == 1M);

            var deposit2 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address2", source.NextDepositId(), $"1{txid}", 12, 31, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address3", source.NextDepositId(), $"1{txid}", 13, 11, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address4", source.NextDepositId(), $"1{txid}", 14, 15, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address5", source.NextDepositId(), $"1{txid}", 16, 16, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address6", source.NextDepositId(), $"1{txid}", 18, 17, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = activeJar.CreateDraftDeposit(itIsThePresent, now, "address7", source.NextDepositId(), $"1{txid}", 19, 91, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(activeJar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = activeJar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
           
            Assert.IsTrue(activeJar.Amount == 6M);
            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == activeJar.PreviousLegacyJar.Version);
            Assert.IsTrue(tank1.ContainerColor == string.Empty);
            tank1.ChangeColor(green);
            Assert.IsTrue(tank1.ContainerColor == green);

            activeJar = source.Jar;
            activeJar.CreateDraftDeposit(itIsThePresent, now, "address1", 1, $"1{txid}", 1, 22, "tb1q4...", 1M, 100000, "USD", 100, 1, 1, domain);
            Assert.IsTrue(activeJar.Amount == 7M);

            Assert.IsTrue(activeJar.AvailableAmount == 0);
            activeJar.ConfirmDeposit(itIsThePresent, now, deposit2);
            Assert.IsTrue(activeJar.AvailableAmount == 1M);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = activeJar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == activeJar.PreviousLegacyJar.Version);
            tank2.ChangeColor(yellow);
            tank1.ChangeColor(yellow);
            Assert.IsTrue(tank1.ContainerColor == yellow);
            tankId = source.NextTankId();
            var mergedTank = tank1.MergeWith(itIsThePresent, tankId, DateTime.Now, tank2);
            var newtank2 = source.FindTank(tank2.Id);
            Assert.IsTrue(mergedTank.ExplandedDeposits.Count() == 2);
        }

        [TestMethod]
        public void TestTankWithDeposits()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now.AddDays(2), source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);


            var tanks = source.BuildTankWithDepositsBetween(now.AddDays(-1), now.AddDays(-1), Tank.FilterTankStatus.ALL);
            Assert.AreEqual(0, tanks.Count);

            tanks = source.BuildTankWithDepositsBetween(now, now.AddDays(1), Tank.FilterTankStatus.ALL);
            Assert.AreEqual(3, tanks.Count);

            tanks = source.BuildTankWithDepositsBetween(now.AddDays(2), now.AddDays(2), Tank.FilterTankStatus.ALL);
            Assert.AreEqual(1, tanks.Count);

            tanks = source.BuildTankWithDepositsFrom(now.AddDays(2), Tank.FilterTankStatus.ALL);

            Assert.AreEqual(1, tanks.Count);

            tanks = source.BuildTankWithDepositsUpTo(now.AddDays(2), Tank.FilterTankStatus.ALL);

            Assert.AreEqual(4, tanks.Count);
        }

        [TestMethod]
        public void TestCreateTankWithTank()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            liquid.Source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1M);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquid.Source.Jar.Amount == 1M);

            var deposit2 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = liquid.Source.CreateDraftDeposit(itIsThePresent, now, liquid.Source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquid.Source.Jar.Amount == 7M);

            int tankId = liquid.Source.NextTankId();
            int jarVerion = liquid.Source.NextJarVersion();

            var tank1 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquid.Source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank2 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank3 = liquid.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, liquid.Source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            liquid.Source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = liquid.Source.NextTankId();
            jarVerion = liquid.Source.NextJarVersion();

            var tank4 = liquid.Source.Jar.CreateEmptyTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion);

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.IsTrue(tank4.Version == 1);
            Assert.AreEqual(0, tank4.Amount);
            Assert.AreEqual(0, tank4.ExplandedDeposits.Count());
            Assert.AreEqual(0, tank4.FlattenedList.Count());
        }

        [TestMethod]
        public void TestMoveDepositTankToTank()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateEmptyTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion);

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.AreEqual(0, tank4.Amount);
            Assert.AreEqual(0, tank4.ExplandedDeposits.Count());
            Assert.AreEqual(0, tank4.FlattenedList.Count());

            var existDeposit = tank4.ExistDeposit(deposit2.Id);
            if (existDeposit)
            {
                Assert.Fail("The deposit should not exist in the tank");
            }
            else
            {
                tank2.MoveDepositToTank(tank4.Id, deposit2);
            }

            Assert.AreEqual(1, tank4.ExplandedDeposits.Count());
            Assert.AreEqual(0, tank2.ExplandedDeposits.Count());
        }

        [TestMethod]
        public void PutAndGet_ReturnsInsertedValue()
        {
            var cache = new LRUCache<string, int>(2);
            cache.Put("a", 1);
            cache.Put("b", 2);

            Assert.AreEqual(1, cache.Get("a"));
            Assert.AreEqual(2, cache.Get("b"));
        }

        [TestMethod]
        public void ExceedCapacity_EvictsLeastRecentlyUsed()
        {
            var cache = new LRUCache<string, int>(2);
            cache.Put("a", 1);
            cache.Put("b", 2);
            cache.Get("a"); // "a" is now most recently used
            cache.Put("c", 3); // "b" should be evicted

            Assert.IsTrue(cache.TryGet("a", out var valueA));
            Assert.AreEqual(1, valueA);
            Assert.IsFalse(cache.TryGet("b", out _));
            Assert.IsTrue(cache.TryGet("c", out var valueC));
            Assert.AreEqual(3, valueC);
        }

        [TestMethod]
        public void Put_SameKey_UpdatesValueAndMovesToFront()
        {
            var cache = new LRUCache<string, int>(2);
            cache.Put("a", 1);
            cache.Put("a", 99);

            Assert.AreEqual(99, cache.Get("a"));
            Assert.AreEqual(1, cache.Count);
        }

        [TestMethod]
        public void Get_NonExistentKey_Throws()
        {
            var cache = new LRUCache<string, int>(2);
            Assert.Throws<KeyNotFoundException>(() => cache.Get("x"));
        }

        [TestMethod]
        public void TestCreateTankWithTankLRU()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateEmptyTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion);

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.AreEqual(0, tank4.Amount);
            Assert.AreEqual(0, tank4.ExplandedDeposits.Count());

            var list = source.RecentTanks();
            Assert.AreEqual(4, list.Count());

        }

        [TestMethod]
        public void TestCreateTankWithTankInside()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            Assert.IsTrue(tank1 != null);
            Assert.IsTrue(tank1.Id == tankId);
            Assert.IsTrue(tank1.Name == "Tank1");
            Assert.IsTrue(tank1.Version == source.Jar.PreviousLegacyJar.Version);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(tank1 != tank2);

            Assert.IsTrue(tank2 != null);
            Assert.IsTrue(tank2.Id == tankId);
            Assert.IsTrue(tank2.Name == "Tank2");
            Assert.IsTrue(tank2.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            Assert.IsTrue(tank3 != null);
            Assert.IsTrue(tank3.Id == tankId);
            Assert.IsTrue(tank3.Name == "Tank3");
            Assert.IsTrue(tank3.Version == 1);


            source.ConfirmDeposit(itIsThePresent, now, deposit4);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateEmptyTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion);

            Assert.IsTrue(tank4 != null);
            Assert.IsTrue(tank4.Id == tankId);
            Assert.IsTrue(tank4.Name == "Tank4");
            Assert.AreEqual(0, tank4.Amount);
            Assert.AreEqual(0, tank4.ExplandedDeposits.Count());

            var list = source.RecentTanks();
            Assert.AreEqual(4, list.Count());

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();
            var listTankIds = new List<int> { tank4.Id,tank2.Id};
            var tank5 = source.Jar.CreateTankWithTank(itIsThePresent, now, tankId, "Tank5", "Fondos para navidad", listTankIds);
            Assert.IsTrue(tank5 != null);
            Assert.IsTrue(tank5.Id == tankId);
            Assert.IsTrue(tank5.Name == "Tank5");
            Assert.IsTrue(tank5.ExplandedDeposits.Count() == 1);
            Assert.IsTrue(tank5.DescendantTanks(false).Count() == 2); 

            Assert.AreEqual(deposit2.Amount, tank5.Amount);
            listTankIds = new List<int> { tank5.Id};
            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();
            var tank6 = source.Jar.CreateTankWithTank(itIsThePresent, now, tankId, "Tank6", "Fondos para navidad2", listTankIds);
            Assert.IsTrue(tank6.ExplandedDeposits.Count() == 1);
            Assert.IsTrue(tank6.DescendantTanks(false).Count() == 3);
            Assert.IsTrue(tank6.Amount == deposit2.Amount);
        }

        [TestMethod]
        public void TestCreateTankWithTankInside2()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank4 = source.Jar.CreateEmptyTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion);

            source.ConfirmDeposit(itIsThePresent, now, deposit4);
            source.ConfirmDeposit(itIsThePresent, now, deposit5);
            
            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 1);

            jarVerion = source.NextJarVersion();
            source.Jar.MoveToTank(itIsThePresent, now, jarVerion, tank4.Id, source.Jar.ConfirmedDeposits());
            
            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 2);

            tank4.MoveToTanks(new List<int> { tank1.Id, tank2.Id, tank3.Id });
            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 3);

            tankId = source.NextTankId();
            var tank5 = source.Jar.CreateTankWithTank(itIsThePresent, now, tankId, "Tank5", "Fondos para navidad", new List<int>() { tank4.Id });            
            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 4);

            source.ConfirmDeposit(itIsThePresent, now, deposit6);
            source.ConfirmDeposit(itIsThePresent, now, deposit7);

            jarVerion = source.NextJarVersion();
            source.Jar.MoveToTank(itIsThePresent, now, jarVerion, tank5.Id, source.Jar.ConfirmedDeposits());
            tank5 = source.FindTank(tank5.Id) as TankReady;

            Assert.IsTrue(tank5.Amount == 7M);
        }

        [TestMethod]
        public void TestMoveTankToTank()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            source.ConfirmDeposit(itIsThePresent, now, deposit4);
            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 1);

            tank3.MoveToTanks(new List<int> { tank1.Id, tank2.Id });
            tank3 = source.FindTank(tank3.Id) as TankReady;
            Assert.IsTrue(tank3.Version == 2);

            tank4.MoveToTanks(new List<int> { tank3.Id });
            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 2);

            source.ConfirmDeposit(itIsThePresent, now, deposit6);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();
            var tank5 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank5", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            
            tank5.MoveToTanks(new List<int> { tank4.Id });
            tank5 = source.FindTank(tank5.Id) as TankReady;
            Assert.IsTrue(tank5.Version == 2);
            Assert.IsTrue(tank5.Amount == 5M);

            var summary = tank5.BuildMonthlySummary(DateTime.MinValue, DateTime.MinValue, "", FilterContainerType.ALL, "");
            Assert.IsTrue(summary.TankTotalAmount == 5M);
            Assert.IsTrue(summary.TotalReceivedAmount == 500M);
        }

        [TestMethod]
        public void TestBug9758ParentTank()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            source.ConfirmDeposit(itIsThePresent, now, deposit4);
            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 1);

            tank3.MoveToTanks(new List<int> { tank1.Id, tank2.Id });
            tank3 = source.FindTank(tank3.Id) as TankReady;
            Assert.IsTrue(tank3.Version == 2);

            tank4.MoveToTanks(new List<int> { tank3.Id });
            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 2);

            source.ConfirmDeposit(itIsThePresent, now, deposit6);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();
            var tank5 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank5", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tank5.MoveToTanks(new List<int> { tank4.Id });
            tank5 = source.FindTank(tank5.Id) as TankReady;
            Assert.IsTrue(tank5.Version == 2);
            Assert.IsTrue(tank5.Amount == 5M);

            var summary = tank5.BuildMonthlySummary(DateTime.MinValue, DateTime.MinValue, "", FilterContainerType.ALL, "");
            Assert.IsTrue(summary.TankTotalAmount == 5M);
            Assert.IsTrue(summary.TotalReceivedAmount == 500M);

            var onlyParentTanks = source.BuildTankWithDepositsBetween(DateTime.MinValue,DateTime.MaxValue,FilterTankStatus.ALL);
            Assert.IsFalse(onlyParentTanks.Contains(tank1));
            Assert.IsFalse(onlyParentTanks.Contains(tank2));
            Assert.IsFalse(onlyParentTanks.Contains(tank3));
            Assert.IsFalse(onlyParentTanks.Contains(tank4));
            Assert.IsTrue(onlyParentTanks.Contains(tank5));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank2));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank3));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank4));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank5));
        }


        [TestMethod]
        public void TestFilteredWithColorOrContainerTank()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            Liquid liquid = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "ETH");
            Source source = liquid.Source;
            source.AddXpub(new Xpub("dasfsdfdsfsd"));

            liquid.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 55, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 1M);

            source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(source.Jar.Amount == 1M);

            var deposit2 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 21, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit3 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 20, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit4 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 23, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit5 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 27, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit6 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 2, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            var deposit7 = source.CreateDraftDeposit(itIsThePresent, now, source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(source.Jar.Amount == 7M);

            int tankId = source.NextTankId();
            int jarVerion = source.NextJarVersion();

            var tank1 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            Assert.IsTrue(source.Jar.Amount == 6M);

            source.ConfirmDeposit(itIsThePresent, now, deposit2);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();
            string blackColor = "#000";
            var tank2 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank2", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            tank2.ChangeColor(blackColor);
            source.ConfirmDeposit(itIsThePresent, now, deposit3);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();
            string whiteColor = "#ffffffff";
            var tank3 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank3", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());
            tank3.ChangeColor(whiteColor);
            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();

            source.ConfirmDeposit(itIsThePresent, now, deposit4);
            var tank4 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank4", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tank4 = source.FindTank(tank4.Id) as TankReady;
            Assert.IsTrue(tank4.Version == 1);

            tank3.MoveToTanks(new List<int> { tank1.Id, tank2.Id });
            tank3 = source.FindTank(tank3.Id) as TankReady;
            Assert.IsTrue(tank3.Version == 2);

            tank4.MoveToTanks(new List<int> { tank3.Id });
            tank4 = source.FindTank(tank4.Id) as TankReady;
            var summaryList = tank4.BuildMonthlySummary(DateTime.MinValue, DateTime.MaxValue, "", FilterContainerType.CONTAINER, "");
            Assert.IsTrue(tank4.Version == 2);
            Assert.IsTrue(summaryList.TotalTanks == 1);

            summaryList = tank4.BuildMonthlySummary(DateTime.MinValue, DateTime.MaxValue, "", FilterContainerType.TRANSACTION, "");
            Assert.IsTrue(tank4.Version == 2);
            Assert.IsTrue(summaryList.TotalTanks == 0);
            Assert.IsTrue(summaryList.MonthlySummaries.Count() == 1);

            source.ConfirmDeposit(itIsThePresent, now, deposit6);

            tankId = source.NextTankId();
            jarVerion = source.NextJarVersion();
            var tank5 = source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank5", "Fondos para navidad", jarVerion, source.Jar.ConfirmedDeposits());

            tank5.MoveToTanks(new List<int> { tank4.Id });
            tank5 = source.FindTank(tank5.Id) as TankReady;
            Assert.IsTrue(tank5.Version == 2);
            Assert.IsTrue(tank5.Amount == 5M);

            var summary = tank5.BuildMonthlySummary(DateTime.MinValue, DateTime.MinValue, "", FilterContainerType.ALL, "");
            Assert.IsTrue(summary.TankTotalAmount == 5M);
            Assert.IsTrue(summary.TotalReceivedAmount == 500M);

            var onlyParentTanks = source.BuildTankWithDepositsBetween(DateTime.MinValue, DateTime.MaxValue, FilterTankStatus.ALL);
            Assert.IsFalse(onlyParentTanks.Contains(tank1));
            Assert.IsFalse(onlyParentTanks.Contains(tank2));
            Assert.IsFalse(onlyParentTanks.Contains(tank3));
            Assert.IsFalse(onlyParentTanks.Contains(tank4));
            Assert.IsTrue(onlyParentTanks.Contains(tank5));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank2));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank3));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank4));
            Assert.IsTrue(source.RootHierarchyTanksAndDescendants().Contains(tank5));
        }
    }
}
